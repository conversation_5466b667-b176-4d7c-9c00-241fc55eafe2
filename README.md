# RSGlider API

> Comprehensive NestJS API for the RSGlider platform with developer marketplace, BTCPay Server integration, and advanced session management.

## 🚀 Features

### 🔐 **Authentication & Security**
- JWT-based authentication with refresh token rotation
- Two-factor authentication (TOTP) with backup codes
- Role-based access control (RBAC)
- Comprehensive session management across platforms
- **SecureLogger**: Automatic sanitization of sensitive data in logs
- **Production-safe error handling**: Database queries and sensitive info never exposed

### 🧑‍💻 **Developer Marketplace**
- Admin-controlled Gitea integration with SSO
- Repository-to-marketplace publishing
- Real-time analytics and revenue tracking
- Flexible BTCPay Server payouts (Bitcoin-only)

### 💳 **Payment System**
- Direct Bitcoin payments via BTCPay Server
- No stored funds (security-first approach)
- Real-time payment tracking with webhooks
- Automated revenue sharing and payouts

### 📱 **Multi-Platform Session & Device Control**
- Desktop app (Tauri) device registration and limits
- Web app device verification with email validation
- Bot session management with subscription-based limits
- Cross-platform session synchronization
- **Device management API:** Register, list, and remove devices for user accounts

## 📁 Project Structure

```
rsglider-api/
├── src/                          # NestJS Application Source
│   ├── admin/                    # Admin management module
│   │   ├── admin.controller.ts   # Admin operations (users, roles, permissions)
│   │   ├── permissions.service.ts # Permission management
│   │   └── roles.service.ts      # Role management
│   ├── auth/                     # Authentication & Authorization
│   │   ├── auth.controller.ts    # Login, register, 2FA, token refresh
│   │   ├── auth.service.ts       # Authentication business logic
│   │   └── strategies/           # JWT and local auth strategies
│   ├── client/                   # Client application management
│   │   └── client.controller.ts  # Client releases and updates
│   ├── common/                   # Shared utilities and services
│   │   ├── decorators/           # Custom decorators (roles, current user)
│   │   ├── filters/              # Exception filters (global, all exceptions)
│   │   ├── guards/               # Auth guards (JWT, roles, throttler)
│   │   ├── interceptors/         # Logging and transform interceptors
│   │   ├── services/             # Core business services
│   │   │   ├── client-releases.service.ts    # Client version management
│   │   │   ├── developer-management.service.ts # Developer operations
│   │   │   ├── file-uploads.service.ts       # File handling
│   │   │   ├── gitea.service.ts              # Gitea API integration
│   │   │   ├── redis.service.ts              # Redis operations
│   │   │   ├── repository-sync.service.ts    # Repository synchronization
│   │   │   ├── s3.service.ts                 # AWS S3 integration
│   │   │   └── webhook-processor.service.ts  # Webhook handling
│   │   └── utils/                # Utility functions
│   │       └── secure-logger.ts  # Security-focused logging utility
│   ├── config/                   # Configuration modules
│   │   ├── database.config.ts    # Database configuration
│   │   ├── jwt.config.ts         # JWT configuration
│   │   └── throttler.config.ts   # Rate limiting configuration
│   ├── database/                 # Database layer
│   │   ├── migrations/           # Drizzle database migrations
│   │   └── schema/               # Database schema definitions
│   │       ├── users.schema.ts           # User accounts
│   │       ├── user-sessions.schema.ts   # Session management
│   │       ├── devices.schema.ts         # Device registration
│   │       ├── roles.schema.ts           # RBAC roles
│   │       ├── permissions.schema.ts     # RBAC permissions
│   │       ├── gitea-profiles.schema.ts  # Gitea integration
│   │       ├── gitea-repositories.schema.ts # Repository data
│   │       ├── marketplace-items.schema.ts  # Store items
│   │       ├── client-releases.schema.ts    # Client versions
│   │       └── file-uploads.schema.ts       # File metadata
│   ├── developer/                # Developer marketplace
│   │   ├── developer.controller.ts # Developer operations
│   │   └── dto/                  # Developer-specific DTOs
│   │       ├── gitea-profile.dto.ts      # Gitea profile data
│   │       ├── repository-analytics.dto.ts # Analytics data
│   │       ├── payout-settings.dto.ts    # Payment configuration
│   │       └── marketplace-metadata.dto.ts # Store metadata
│   ├── store/                    # Marketplace store
│   │   └── dto/                  # Store-related DTOs
│   │       ├── store-item.dto.ts         # Store item data
│   │       ├── cart.dto.ts               # Shopping cart
│   │       ├── pricing-structure.dto.ts  # Pricing models
│   │       └── subscription-plan.dto.ts  # Subscription tiers
│   ├── uploads/                  # File upload handling
│   │   └── uploads.controller.ts # File upload endpoints
│   ├── users/                    # User management
│   │   ├── users.controller.ts   # User operations
│   │   ├── users.service.ts      # User business logic
│   │   └── dto/                  # User-related DTOs
│   │       ├── user.dto.ts               # User profile data
│   │       ├── session.dto.ts            # Session information
│   │       ├── device.dto.ts             # Device registration
│   │       ├── two-factor-*.dto.ts       # 2FA operations
│   │       └── marketplace-item.dto.ts   # User's marketplace items
│   ├── webhooks/                 # Webhook processing
│   │   ├── webhooks.controller.ts # Webhook endpoints
│   │   └── dto/                  # Webhook DTOs
│   │       └── gitea-webhook.dto.ts # Gitea webhook payloads
│   └── main.ts                   # Application entry point
├── test/                         # Test configuration
│   ├── database-setup.ts         # Test database utilities
│   └── setup.ts                  # Global test configuration
├── api-docs/                     # API Documentation
│   ├── openapi.yaml              # Complete OpenAPI 3.0.3 specification
│   ├── README.md                 # API documentation overview
│   └── examples/                 # Testing resources
├── docs/                         # Implementation guides
│   ├── BTCPAY_INTEGRATION.md     # Bitcoin payment integration
│   ├── GITEA_INTEGRATION_PLAN.md # Repository management
│   ├── SESSION_MANAGEMENT.md     # Multi-platform sessions
│   └── testing/                  # Testing documentation
├── docker/                       # Docker configurations
│   ├── postgres/                 # PostgreSQL setup
│   ├── redis/                    # Redis configuration
│   └── gitea/                    # Gitea integration setup
├── coverage/                     # Test coverage reports
├── dist/                         # Compiled TypeScript output
└── uploads/                      # Local file storage (development)
```

## 🛠️ Development Setup

### Prerequisites
- Node.js 22+ LTS and npm 10+
- Docker and Docker Compose
- PostgreSQL 17 (via Docker)
- Redis 7 (via Docker)

### Quick Start
```bash
# Clone repository
git clone https://github.com/rsglider/rsglider-api.git
cd rsglider-api

# Set up local infrastructure (PostgreSQL + Redis)
ppnpm run docker:setup

# Install Node.js dependencies
npm install

# Start development server
pnpm run start:dev
```

### Manual Setup
```bash
# Start just the databases
pnpm run docker:up

# Stop databases
pnpm run docker:down

# View database logs
pnpm run docker:logs

# Access database shell
pnpm run db:shell

# Access Redis shell
pnpm run redis:shell
```

### Local Development
The setup script creates a `.env` file with development defaults:

```bash
# Database (Docker)
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=rsglider
DATABASE_USER=rsglider
DATABASE_PASSWORD=rsglider_dev_password

# Redis (Docker)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=rsglider_redis_password

# JWT (Development - change in production!)
JWT_SECRET=dev_jwt_secret_change_in_production
```

For production, copy `.env.example` and update with real values.

## �️ Database Schema

The application uses **PostgreSQL** with **Drizzle ORM** for type-safe database operations:

### Core Tables
- **`users`**: User accounts, profiles, and authentication data
- **`user_sessions`**: Active user sessions across devices
- **`devices`**: Registered devices (desktop, web, bot)
- **`refresh_tokens`**: JWT refresh token management
- **`roles`** & **`permissions`**: RBAC system
- **`user_roles`** & **`role_permissions`**: RBAC relationships

### Developer & Marketplace
- **`gitea_profiles`**: Gitea account integration data
- **`gitea_repositories`**: Repository metadata and sync status
- **`marketplace_items`**: Published marketplace items
- **`file_uploads`**: File metadata and S3 references

### Client Management
- **`client_releases`**: Desktop client versions and updates

### Database Operations
```bash
# Generate new migration
pnpm run db:generate

# Apply migrations
pnpm run db:migrate

# Push schema changes (development)
pnpm run db:push

# Open Drizzle Studio (database GUI)
pnpm run db:studio
```

## 🛠️ Technology Stack

### **Backend Framework**
- **NestJS 11**: Enterprise-grade Node.js framework
- **TypeScript**: Type-safe development
- **Drizzle ORM**: Type-safe database operations
- **PostgreSQL 17**: Primary database
- **Redis 7**: Session storage and caching

### **Authentication & Security**
- **JWT**: Access and refresh tokens
- **Passport.js**: Authentication strategies
- **Bcrypt**: Password hashing
- **Speakeasy**: TOTP two-factor authentication
- **Rate Limiting**: Request throttling protection

### **File Storage & Processing**
- **AWS S3**: File storage with presigned URLs
- **Multer**: File upload handling
- **MIME Type Validation**: Security-focused file validation

### **External Integrations**
- **Gitea API**: Repository management
- **BTCPay Server**: Bitcoin payment processing
- **Webhook Processing**: Event-driven architecture

### **Development & Testing**
- **Jest**: Unit and integration testing
- **Supertest**: API endpoint testing
- **Docker Compose**: Local development environment
- **ESLint & Prettier**: Code quality and formatting
- **OpenAPI/Swagger**: API documentation

### **Monitoring & Logging**
- **SecureLogger**: Custom logging with data sanitization
- **Global Exception Filter**: Centralized error handling
- **Request/Response Interceptors**: Logging and transformation

## �📚 API Documentation

### Interactive Documentation
- **Development**: http://localhost:3000/api/docs
- **Staging**: https://api-staging.rsglider.com/api/docs
- **Production**: https://api.rsglider.com/api/docs

### Testing Resources
- **Postman Collection**: `api-docs/examples/postman-collection.json`
- **cURL Examples**: `api-docs/examples/curl-examples.md`
- **Environment Files**: `api-docs/examples/environments.json`

## 🧪 Testing & Validation

### API Validation
```bash
# Validate OpenAPI specification
pnpm run validate:api

# Lint YAML files
pnpm run validate:yaml

# Check OpenAPI compliance
pnpm run validate:openapi

# Run Postman tests
pnpm run postman:test
```

### Unit & Integration Tests

#### 🔇 **Clean Testing (Default)**
```bash
# Run all tests with clean output
pnpm test

# Run tests in watch mode
pnpm run test:watch

# Run tests with coverage
pnpm run test:cov

# Run specific test file
pnpm test src/auth/auth.service.spec.ts
```

#### 🔊 **Debug Testing (Full Logs)**
When you need to see all NestJS service logs, database errors, and debug information:

```bash
# Run tests with full logging
TEST_DEBUG=true pnpm test

# Run coverage with full logging
TEST_DEBUG=true pnpm run test:cov

# Debug specific test file
TEST_DEBUG=true pnpm test src/auth/auth.service.spec.ts

# Debug with watch mode
TEST_DEBUG=true pnpm run test:watch
```

#### 📊 **Coverage & Reporting**
```bash
# Generate detailed coverage reports
pnpm run test:cov:detailed

# Generate tree coverage view
pnpm run test:cov:tree

# Run with coverage threshold
pnpm run test:cov:threshold

# Open coverage report in browser
pnpm run test:cov:open
```

#### 🎯 **Testing Best Practices**
- **Default mode**: Clean output for normal development
- **Debug mode**: Use `TEST_DEBUG=true` when investigating test failures
- **Integration tests**: Use real database setup with Drizzle migrations
- **Unit tests**: Focus on business logic with proper mocking
- **Coverage target**: Aim for 80% overall coverage

## 🧪 Development Tools

### Database Management
- **PostgreSQL**: localhost:5432 (use your preferred SQL client)
- **Direct access**: `pnpm run db:shell`

### Redis Management
- **Redis Commander**: http://localhost:8091 (Redis admin interface)
- **Direct access**: `pnpm run redis:shell`

### API Documentation
- **Swagger UI**: http://localhost:3000/api/docs (when NestJS is running)

## 🏗️ Core Modules & Features

### 🔐 **Authentication Module** (`src/auth/`)
- **JWT Authentication**: Access and refresh token management
- **Two-Factor Authentication**: TOTP with backup codes
- **Registration & Login**: Complete user onboarding
- **Password Security**: Bcrypt hashing with salt rounds
- **Session Management**: Multi-device session tracking

### 👥 **User Management** (`src/users/`)
- **Profile Management**: Bio, avatar, preferences
- **Device Registration**: Desktop, web, and bot device limits
- **Session Control**: Active session monitoring and termination
- **Subscription Tiers**: Free, Pro, Enterprise with different limits
- **Analytics**: User activity and revenue tracking

### 🛡️ **Admin Module** (`src/admin/`)
- **User Administration**: View, edit, suspend user accounts
- **Role Management**: Create and assign roles with granular permissions
- **Permission System**: Fine-grained access control
- **Developer Promotion**: Upgrade users to developer status
- **System Analytics**: Platform-wide metrics and insights

### 🧑‍💻 **Developer Module** (`src/developer/`)
- **Gitea Integration**: Automatic account provisioning
- **Repository Management**: Sync, publish, and manage repositories
- **Analytics Dashboard**: Revenue, downloads, and performance metrics
- **Payout Management**: Configure payment preferences and schedules
- **Marketplace Publishing**: Convert repositories to marketplace items

### 🏪 **Store Module** (`src/store/`)
- **Marketplace Browsing**: Search and filter marketplace items
- **Shopping Cart**: Add, remove, and manage cart items
- **Pricing Models**: One-time, subscription, and tiered pricing
- **Volume Discounts**: Bulk purchase incentives
- **Coupon System**: Promotional codes and discounts

### 📁 **File Upload Module** (`src/uploads/`)
- **S3 Integration**: Secure file storage with AWS S3
- **File Validation**: Type, size, and security checks
- **Presigned URLs**: Secure direct upload and download
- **Metadata Tracking**: File information and usage analytics
- **Image Processing**: Automatic optimization and resizing

### 🔗 **Webhook Module** (`src/webhooks/`)
- **Gitea Webhooks**: Repository event processing
- **BTCPay Webhooks**: Payment status updates
- **Signature Verification**: Secure webhook validation
- **Event Processing**: Automated responses to external events

### 🖥️ **Client Module** (`src/client/`)
- **Release Management**: Desktop client version control
- **Update Distribution**: Automatic update delivery
- **Platform Support**: Multi-platform client management
- **Version Analytics**: Update adoption tracking

### 🔧 **Common Services** (`src/common/services/`)
- **Database Service**: Drizzle ORM integration
- **Redis Service**: Caching and session storage
- **Gitea Service**: Repository API integration
- **S3 Service**: File storage operations
- **Repository Sync**: Automated repository synchronization
- **Webhook Processor**: Event handling and routing

## 📖 API Endpoints Overview

### 🔐 **Authentication** (`/auth`)
```
POST   /auth/register           # User registration
POST   /auth/login              # Login with 2FA support
POST   /auth/refresh            # Token refresh
POST   /auth/logout             # Session termination
POST   /auth/2fa/setup          # Enable two-factor authentication
POST   /auth/2fa/verify         # Verify 2FA token
POST   /auth/2fa/disable        # Disable 2FA
GET    /auth/2fa/backup-codes   # Generate backup codes
```

### 👥 **User Management** (`/users`)
```
GET    /users/me                # Current user profile
PUT    /users/me                # Update profile
GET    /users/me/sessions       # List active sessions
DELETE /users/me/sessions/{id}  # Terminate session
GET    /users/me/devices        # List registered devices
POST   /users/me/devices        # Register new device
DELETE /users/me/devices/{id}   # Remove device
GET    /users/me/analytics      # User analytics
```

### 🧑‍💻 **Developer** (`/developer`)
```
GET    /developer/profile       # Developer profile
POST   /developer/create        # Create developer account
GET    /developer/repositories  # List repositories
POST   /developer/repositories/{id}/publish  # Publish to marketplace
GET    /developer/analytics     # Revenue and performance metrics
PUT    /developer/payout-settings  # Configure payouts
GET    /developer/payouts       # Payout history
```

### 🛡️ **Admin** (`/admin`)
```
GET    /admin/users             # List all users
PUT    /admin/users/{id}        # Update user
POST   /admin/users/{id}/promote  # Promote to developer
GET    /admin/roles             # List roles
POST   /admin/roles             # Create role
PUT    /admin/roles/{id}        # Update role
GET    /admin/permissions       # List permissions
POST   /admin/permissions       # Create permission
```

### 📁 **File Uploads** (`/uploads`)
```
POST   /uploads                 # Upload file
GET    /uploads/{id}            # Get file info
GET    /uploads/{id}/download   # Download file
DELETE /uploads/{id}            # Delete file
POST   /uploads/presigned       # Get presigned upload URL
```

### 🔗 **Webhooks** (`/webhooks`)
```
POST   /webhooks/gitea          # Gitea repository events
POST   /webhooks/btcpay         # BTCPay payment events
POST   /webhooks/gitea/test     # Test webhook endpoint
```

### 🖥️ **Client** (`/client`)
```
GET    /client/check_update/{target}/{arch}/{version}  # Check for updates
GET    /client/releases         # List available releases
```

## 🔒 Security Features

### SecureLogger Utility
The API includes a comprehensive logging system that automatically sanitizes sensitive data:

```typescript
import { SecureLogger } from '@/common/utils/secure-logger';

const logger = new SecureLogger('MyService');

// Automatically redacts sensitive fields
logger.log({ username: 'john', password: 'secret123' });
// Output: { username: 'john', password: '[REDACTED]' }

// Database errors are sanitized in production
logger.error('Failed query: SELECT * FROM users WHERE password = $1');
// Production: 'Database operation failed'
// Development: Full error details
```

#### Sensitive Field Detection
Automatically redacts fields containing:
- `password`, `token`, `secret`, `key`
- `authorization`, `auth`, `credential`
- `private`, `session`, `cookie`, `jwt`
- `refresh_token`, `access_token`, `api_key`
- `client_secret`, `client_id`

#### Specialized Logging Methods
```typescript
// Database operations
logger.logDatabaseOperation('INSERT', 'users', true);

// Authentication events
logger.logAuthEvent('login', userId, true, { ip: '127.0.0.1' });

// API requests with URL sanitization
logger.logApiRequest('GET', '/api/users?token=secret', 200, userId, 150);
```

### Global Exception Filter
- **Production**: Sanitizes all error responses and logs
- **Development**: Full error details for debugging
- **Test**: Suppressed logging to prevent noise

## 🔧 Configuration

The RSGlider API uses a flexible configuration system that allows customization of all platform settings through environment variables and configuration files.

### Payout Settings
Payout configurations are fully customizable via environment variables:

```bash
# Payout Configuration (.env)
DEFAULT_PAYOUT_FREQUENCY=weekly    # daily, weekly, biweekly, monthly, quarterly
MINIMUM_PAYOUT_AMOUNT=50          # Minimum payout threshold (USD)
PAYOUT_CURRENCY=USD               # Base currency for calculations
```

Additional payout settings can be configured per developer through the API or admin interface:
- **Frequency options**: Daily, weekly, biweekly, monthly, quarterly
- **Revenue sharing**: Configurable percentage splits
- **Holding periods**: Customizable delay before payouts
- **Payment methods**: BTCPay Server Bitcoin integration

### Session Limits
Session limits are configurable per subscription tier and can be customized through the admin interface:

- **Subscription tiers**: Free, Pro, Enterprise (fully configurable)
- **Session types**: Desktop, web, and bot sessions with individual limits
- **Addon system**: Additional sessions available for purchase
- **Device management**: Per-device session controls

See `src/users/dto/session-limits.dto.ts` and subscription configuration for detailed customization options.

### Configuration Files
- **Environment variables**: `.env.example` for reference, `.env` for local overrides
- **Database config**: `src/config/database.config.ts`
- **JWT settings**: `src/config/jwt.config.ts`
- **Rate limiting**: `src/config/throttler.config.ts`
- **Payout settings**: `src/developer/dto/payout-settings.dto.ts`

For detailed configuration options, see the [Configuration Documentation](docs/) and environment variable examples.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and validation
5. Submit a pull request

### Code Quality
- Follow NestJS conventions
- Add Swagger decorators to all DTOs
- Include unit tests for new features
- Validate OpenAPI spec changes
- **Maintain 80% test coverage** - Use `pnpm run test:cov` to check
- **Use SecureLogger** for all logging to ensure sensitive data protection
- **Test in both modes**: Clean output for CI/CD, debug mode for investigation

## 📄 License

This project is proprietary and confidential. All rights reserved.

## 🆘 Support

- **Documentation**: https://docs.rsglider.com
- **Issues**: https://github.com/rsglider/rsglider-api/issues
- **Discord**: https://discord.gg/rsglider

---

Built with ❤️ by the RSGlider team
