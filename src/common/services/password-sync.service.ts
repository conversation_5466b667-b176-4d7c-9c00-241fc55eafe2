import { BadRequestException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import * as schema from '../../database/schema/index.js';
import { giteaProfiles } from '../../database/schema/gitea-profiles.schema.js';
import { users } from '../../database/schema/users.schema.js';
import { EmailService } from '../../email/email.service.js';
import { GiteaService } from './gitea.service.js';

export interface PasswordSyncResult {
  success: boolean;
  direction: 'rsglider-to-gitea' | 'gitea-to-rsglider';
  userId?: string;
  giteaUsername?: string;
  error?: string;
}

@Injectable()
export class PasswordSyncService {
  private readonly logger = new Logger(PasswordSyncService.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly giteaService: GiteaService,
    private readonly emailService: EmailService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Sync password from RSGlider to Gitea
   * Called when user changes password in RSGlider
   */
  async syncPasswordToGitea(userId: string, newPassword: string): Promise<PasswordSyncResult> {
    try {
      this.logger.log(`Starting password sync RSGlider → Gitea for user: ${userId}`);

      // Get user's Gitea profile
      const [giteaProfile] = await this.db.db
        .select({
          giteaUsername: giteaProfiles.giteaUsername,
          isProvisioned: giteaProfiles.isProvisioned,
          isActive: giteaProfiles.isActive,
        })
        .from(giteaProfiles)
        .where(eq(giteaProfiles.userId, userId))
        .limit(1);

      if (!giteaProfile) {
        this.logger.log(`No Gitea profile found for user ${userId}, skipping password sync`);
        return {
          success: true, // Not an error - user just doesn't have Gitea account
          direction: 'rsglider-to-gitea',
          userId,
        };
      }

      if (!giteaProfile.isProvisioned || !giteaProfile.isActive) {
        this.logger.log(`Gitea profile not provisioned/active for user ${userId}, skipping password sync`);
        return {
          success: true, // Not an error - account not ready for sync
          direction: 'rsglider-to-gitea',
          userId,
          giteaUsername: giteaProfile.giteaUsername,
        };
      }

      // Update password in Gitea
      await this.giteaService.updateUserPassword(giteaProfile.giteaUsername, newPassword);

      this.logger.log(`Successfully synced password RSGlider → Gitea for user: ${userId} (${giteaProfile.giteaUsername})`);

      return {
        success: true,
        direction: 'rsglider-to-gitea',
        userId,
        giteaUsername: giteaProfile.giteaUsername,
      };

    } catch (error) {
      this.logger.error(`Failed to sync password RSGlider → Gitea for user ${userId}:`, error.message);
      
      return {
        success: false,
        direction: 'rsglider-to-gitea',
        userId,
        error: error.message,
      };
    }
  }

  /**
   * Sync password from Gitea to RSGlider
   * Called via webhook when user changes password in Gitea
   */
  async syncPasswordFromGitea(giteaUsername: string, newPasswordHash: string): Promise<PasswordSyncResult> {
    try {
      this.logger.log(`Starting password sync Gitea → RSGlider for Gitea user: ${giteaUsername}`);

      // Find RSGlider user by Gitea username
      const [profile] = await this.db.db
        .select({
          userId: giteaProfiles.userId,
          userEmail: users.email,
          userName: users.name,
        })
        .from(giteaProfiles)
        .innerJoin(users, eq(giteaProfiles.userId, users.id))
        .where(eq(giteaProfiles.giteaUsername, giteaUsername))
        .limit(1);

      if (!profile) {
        throw new NotFoundException(`No RSGlider user found for Gitea username: ${giteaUsername}`);
      }

      // Update password in RSGlider database
      await this.db.db
        .update(users)
        .set({
          password: newPasswordHash, // Gitea should send bcrypt hash
          updatedAt: new Date(),
        })
        .where(eq(users.id, profile.userId));

      // Send password sync notification email
      await this.emailService.sendPasswordSyncNotification(
        profile.userEmail,
        profile.userName || 'User',
        'gitea'
      );

      this.logger.log(`Successfully synced password Gitea → RSGlider for user: ${profile.userId} (${giteaUsername})`);

      return {
        success: true,
        direction: 'gitea-to-rsglider',
        userId: profile.userId,
        giteaUsername,
      };

    } catch (error) {
      this.logger.error(`Failed to sync password Gitea → RSGlider for Gitea user ${giteaUsername}:`, error.message);
      
      return {
        success: false,
        direction: 'gitea-to-rsglider',
        giteaUsername,
        error: error.message,
      };
    }
  }

  /**
   * Handle password sync failure
   * Logs error and optionally notifies user
   */
  async handlePasswordSyncFailure(result: PasswordSyncResult, userEmail?: string): Promise<void> {
    if (result.success) {
      return; // No failure to handle
    }

    this.logger.error(`Password sync failed:`, {
      direction: result.direction,
      userId: result.userId,
      giteaUsername: result.giteaUsername,
      error: result.error,
    });

    // Optionally send failure notification to user
    if (userEmail && this.configService.get('NOTIFY_PASSWORD_SYNC_FAILURES', 'false') === 'true') {
      try {
        await this.emailService.sendPasswordSyncFailure(
          userEmail,
          'User',
          result.direction,
          result.error || 'Unknown error'
        );
      } catch (emailError) {
        this.logger.error('Failed to send password sync failure notification:', emailError.message);
      }
    }
  }

  /**
   * Test password synchronization
   * Used for health checks and testing
   */
  async testPasswordSync(): Promise<{ rsgliderToGitea: boolean; giteaToRsglider: boolean }> {
    try {
      // Test Gitea connection
      const giteaConnected = await this.giteaService.testConnection();
      
      return {
        rsgliderToGitea: giteaConnected,
        giteaToRsglider: true, // Always true if service is running
      };
    } catch (error) {
      this.logger.error('Password sync test failed:', error.message);
      return {
        rsgliderToGitea: false,
        giteaToRsglider: false,
      };
    }
  }
}
