/**
 * WebhooksController Integration Tests
 * Tests the WebhooksController with real database operations
 */

import { INestApplication, ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';
import {
  cleanTestDatabase,
  closeTestDatabase,
  createTestDatabase,
  seedTestDatabase,
  TestDatabase
} from '../../test/database-setup.js';
import { GlobalExceptionFilter } from '../common/filters/global-exception.filter.js';
import { WebhookProcessorService } from '../common/services/webhook-processor.service.js';
import { WebhooksController } from './webhooks.controller.js';

describe('WebhooksController Integration Tests', () => {
  let app: INestApplication;
  let testDb: TestDatabase;
  let testData: any;
  let mockWebhookProcessor: any;
  let validStripePayload: any;

  beforeAll(async () => {
    // Create test database
    testDb = await createTestDatabase();
    testData = await seedTestDatabase(testDb);

    // Define Stripe payload after testData is available
    validStripePayload = {
      id: 'evt_test_webhook',
      object: 'event',
      type: 'payment_intent.succeeded',
      data: {
        object: {
          id: 'pi_test_payment',
          amount: 2000,
          currency: 'usd',
          status: 'succeeded',
          metadata: {
            userId: testData.testUser.id,
            orderId: 'order_123',
          },
        },
      },
    };

    // Mock webhook processor service
    mockWebhookProcessor = {
      verifyWebhookSignature: jest.fn().mockReturnValue(true),
      processRepositoryWebhook: jest.fn().mockResolvedValue({
        processed: true,
        action: 'repository',
        message: 'Repository webhook processed'
      }),
      processPushWebhook: jest.fn().mockResolvedValue({
        processed: true,
        action: 'push',
        message: 'Push webhook processed'
      }),
      processReleaseWebhook: jest.fn().mockResolvedValue({
        processed: true,
        action: 'release',
        message: 'Release webhook processed'
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [WebhooksController],
      providers: [
        {
          provide: 'DB',
          useValue: testDb,
        },
        {
          provide: WebhookProcessorService,
          useValue: mockWebhookProcessor,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key, defaultValue) => {
              const config = {
                'GITEA_WEBHOOK_SECRET': 'test-webhook-secret',
                'STRIPE_WEBHOOK_SECRET': 'test-stripe-secret',
                'NODE_ENV': 'test',
              };
              return config[key] || defaultValue;
            }),
          },
        },
      ],
    }).compile();

    app = module.createNestApplication();
    app.useGlobalFilters(new GlobalExceptionFilter());
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }));
    await app.init();
  });

  afterAll(async () => {
    await app.close();
    await closeTestDatabase();
  });

  beforeEach(async () => {
    await cleanTestDatabase(testDb);
    testData = await seedTestDatabase(testDb);
    jest.clearAllMocks();
  });

  describe('POST /webhooks/gitea', () => {
    const validGiteaPayload = {
      action: 'created',
      repository: {
        id: 1,
        name: 'test-repo',
        full_name: 'testuser/test-repo',
        owner: {
          login: 'testuser',
          id: 1,
        },
      },
    };

    it('should process repository webhook', async () => {
      const response = await request(app.getHttpServer())
        .post('/webhooks/gitea')
        .set('X-Gitea-Event', 'repository')
        .set('X-Gitea-Signature', 'sha256=test-signature')
        .send(validGiteaPayload)
        .expect(200);

      expect(response.body).toMatchObject({
        processed: true,
        action: 'repository',
        message: expect.any(String),
      });
      expect(mockWebhookProcessor.processRepositoryWebhook).toHaveBeenCalledWith(
        validGiteaPayload
      );
    });

    it('should handle push events', async () => {
      const pushPayload = {
        ref: 'refs/heads/main',
        commits: [
          {
            id: 'abc123',
            message: 'Test commit',
            author: {
              name: 'Test User',
              email: '<EMAIL>',
            },
          },
        ],
        repository: {
          id: 1,
          name: 'test-repo',
          full_name: 'testuser/test-repo',
        },
      };

      const response = await request(app.getHttpServer())
        .post('/webhooks/gitea')
        .set('X-Gitea-Event', 'push')
        .set('X-Gitea-Signature', 'sha256=test-signature')
        .send(pushPayload)
        .expect(200);

      expect(response.body).toMatchObject({
        processed: true,
        action: 'push',
        message: expect.any(String),
      });
      expect(mockWebhookProcessor.processPushWebhook).toHaveBeenCalledWith(
        pushPayload
      );
    });

    it('should reject webhooks with invalid signature', async () => {
      mockWebhookProcessor.verifyWebhookSignature.mockReturnValueOnce(false);

      await request(app.getHttpServer())
        .post('/webhooks/gitea')
        .set('X-Gitea-Event', 'repository')
        .set('X-Gitea-Signature', 'sha256=invalid-signature')
        .send(validGiteaPayload)
        .expect(400); // BadRequestException returns 400

      expect(mockWebhookProcessor.processRepositoryWebhook).not.toHaveBeenCalled();
    });

    it('should handle unhandled event types', async () => {
      const response = await request(app.getHttpServer())
        .post('/webhooks/gitea')
        .set('X-Gitea-Event', 'unknown-event')
        .set('X-Gitea-Signature', 'sha256=test-signature')
        .send(validGiteaPayload)
        .expect(200);

      expect(response.body).toMatchObject({
        processed: false,
        action: 'unknown-event',
        message: expect.stringContaining('Unhandled event type'),
      });
    });

    it('should handle webhook processing errors', async () => {
      mockWebhookProcessor.processRepositoryWebhook.mockRejectedValueOnce(
        new Error('Processing failed')
      );

      await request(app.getHttpServer())
        .post('/webhooks/gitea')
        .set('X-Gitea-Event', 'repository')
        .set('X-Gitea-Signature', 'sha256=test-signature')
        .send(validGiteaPayload)
        .expect(400); // BadRequestException returns 400
    });
  });

  describe('POST /webhooks/gitea/test', () => {
    it('should handle test webhook', async () => {
      const response = await request(app.getHttpServer())
        .post('/webhooks/gitea/test')
        .expect(200);

      expect(response.body).toMatchObject({
        message: 'Test webhook received successfully',
        timestamp: expect.any(String),
        headers: expect.objectContaining({
          'content-type': 'application/json',
          'x-gitea-event': 'test',
        }),
      });
    });
  });
});
