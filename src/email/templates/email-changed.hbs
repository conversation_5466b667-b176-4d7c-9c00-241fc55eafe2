<!-- Hero Section with Orange Gradient -->
<div style="background: linear-gradient(135deg, #ff6b35, #f7931e); padding: 48px 32px; border-radius: 20px; text-align: center; margin-bottom: 40px; box-shadow: 0 8px 32px rgba(255, 107, 53, 0.3);">
    <div style="background: rgba(255, 255, 255, 0.15); width: 96px; height: 96px; border-radius: 50%; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(10px); border: 2px solid rgba(255, 255, 255, 0.2);">
        <span style="font-size: 48px;">✅</span>
    </div>
    <h1 style="color: #ffffff; margin: 0 0 16px 0; font-size: 36px; font-weight: 800; text-shadow: 0 2px 8px rgba(0,0,0,0.2); letter-spacing: -0.5px;">Email Changed Successfully</h1>
    <p style="color: rgba(255, 255, 255, 0.95); margin: 0; font-size: 20px; font-weight: 500; text-shadow: 0 1px 4px rgba(0,0,0,0.1);">Hi {{#if name}}{{name}}{{else}}there{{/if}}, your email address has been updated!</p>
</div>

<!-- Success Confirmation -->
<div style="background: linear-gradient(135deg, #ecfdf5, #f0fdf4); border: 2px solid #10b981; border-radius: 16px; padding: 32px; margin-bottom: 40px; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #10b981, #059669, #047857);"></div>
    <div style="display: flex; align-items: center; gap: 20px;">
        <div style="background: linear-gradient(135deg, #10b981, #059669); color: white; width: 64px; height: 64px; border-radius: 20px; display: flex; align-items: center; justify-content: center; font-size: 28px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);">📧</div>
        <div>
            <h2 style="color: #065f46; margin: 0 0 8px 0; font-size: 24px; font-weight: 700;">Email Address Updated</h2>
            <p style="color: #047857; margin: 0; font-size: 18px; font-weight: 500;">Your {{siteName}} account email address was successfully changed and verified</p>
        </div>
    </div>
</div>

<!-- Change Details -->
<div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 16px; padding: 32px; margin: 40px 0; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
    <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 24px;">
        <div style="background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; width: 48px; height: 48px; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 20px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);">📋</div>
        <div>
            <h3 style="color: #1f2937; margin: 0 0 4px 0; font-size: 18px; font-weight: 700;">Change Details</h3>
            <p style="color: #6b7280; margin: 0; font-size: 14px;">Here's what changed on your account:</p>
        </div>
    </div>
    <div style="display: grid; gap: 16px;">
        <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f8fafc; border-radius: 12px;">
            <div style="background: #ef4444; color: white; width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 14px; flex-shrink: 0;">📧</div>
            <div>
                <strong style="color: #1f2937; font-size: 15px;">Previous Email:</strong>
                <span style="color: #6b7280; font-size: 15px; margin-left: 8px;">{{#if oldEmail}}{{oldEmail}}{{else}}Your previous email address{{/if}}</span>
            </div>
        </div>
        <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f8fafc; border-radius: 12px;">
            <div style="background: #10b981; color: white; width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 14px; flex-shrink: 0;">✅</div>
            <div>
                <strong style="color: #1f2937; font-size: 15px;">New Email:</strong>
                <span style="color: #6b7280; font-size: 15px; margin-left: 8px;">{{newEmail}}</span>
            </div>
        </div>
        <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f8fafc; border-radius: 12px;">
            <div style="background: #3b82f6; color: white; width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 14px; flex-shrink: 0;">⏰</div>
            <div>
                <strong style="color: #1f2937; font-size: 15px;">Changed:</strong>
                <span style="color: #6b7280; font-size: 15px; margin-left: 8px;">{{formatDate changeTime}}</span>
            </div>
        </div>
    </div>
</div>

<!-- What This Means Section -->
<div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 16px; padding: 32px; margin: 40px 0; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
    <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 24px;">
        <div style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white; width: 48px; height: 48px; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 20px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);">💡</div>
        <div>
            <h3 style="color: #1f2937; margin: 0 0 4px 0; font-size: 18px; font-weight: 700;">What This Means</h3>
            <p style="color: #6b7280; margin: 0; font-size: 14px;">Important changes to your account:</p>
        </div>
    </div>
    <div style="display: grid; gap: 16px;">
        <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f8fafc; border-radius: 12px;">
            <div style="background: #10b981; color: white; width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 14px; flex-shrink: 0;">🔄</div>
            <span style="color: #1f2937; font-size: 15px; font-weight: 500;">All future notifications will be sent to your new email address</span>
        </div>
        <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f8fafc; border-radius: 12px;">
            <div style="background: #3b82f6; color: white; width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 14px; flex-shrink: 0;">🔐</div>
            <span style="color: #1f2937; font-size: 15px; font-weight: 500;">You'll need to use your new email address to sign in</span>
        </div>
        <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f8fafc; border-radius: 12px;">
            <div style="background: #f59e0b; color: white; width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 14px; flex-shrink: 0;">📧</div>
            <span style="color: #1f2937; font-size: 15px; font-weight: 500;">This old email address will no longer receive account notifications</span>
        </div>
        <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f8fafc; border-radius: 12px;">
            <div style="background: #8b5cf6; color: white; width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 14px; flex-shrink: 0;">✅</div>
            <span style="color: #1f2937; font-size: 15px; font-weight: 500;">Your account security and settings remain unchanged</span>
        </div>
    </div>
</div>

<!-- Access Account Button -->
<div style="text-align: center; margin: 48px 0;">
    <a href="{{baseUrl}}" style="display: inline-block; background: linear-gradient(135deg, #ff6b35, #f7931e); color: #ffffff !important; padding: 20px 40px; border-radius: 16px; text-decoration: none; font-weight: 700; font-size: 18px; border: none; cursor: pointer; box-shadow: 0 8px 24px rgba(255, 107, 53, 0.4); transition: transform 0.2s; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">
        🚀 Access My Account
    </a>
    <p style="margin: 16px 0 0 0; color: #6b7280; font-size: 14px;">Sign in with your new email address</p>
</div>

<!-- Security Warning -->
<div style="background: linear-gradient(135deg, #fef3c7, #fde68a); border: 2px solid #f59e0b; border-radius: 16px; padding: 32px; margin: 40px 0; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #f59e0b, #d97706, #b45309);"></div>
    <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px;">
        <div style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white; width: 64px; height: 64px; border-radius: 20px; display: flex; align-items: center; justify-content: center; font-size: 28px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);">⚠️</div>
        <div>
            <h3 style="color: #92400e; margin: 0 0 8px 0; font-size: 20px; font-weight: 700;">Didn't Make This Change?</h3>
            <p style="color: #b45309; margin: 0; font-size: 16px; font-weight: 500;">If you didn't change your email address, your account may have been compromised. Please contact our support team immediately.</p>
        </div>
    </div>
</div>

<!-- Security Recommendations -->
<div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 16px; padding: 32px; margin: 40px 0; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
    <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 24px;">
        <div style="background: linear-gradient(135deg, #10b981, #059669); color: white; width: 48px; height: 48px; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 20px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);">🛡️</div>
        <div>
            <h3 style="color: #1f2937; margin: 0 0 4px 0; font-size: 18px; font-weight: 700;">Security Recommendations</h3>
            <p style="color: #6b7280; margin: 0; font-size: 14px;">Keep your account secure with these best practices:</p>
        </div>
    </div>
    <div style="display: grid; gap: 16px;">
        <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f8fafc; border-radius: 12px;">
            <div style="background: #10b981; color: white; width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 14px; flex-shrink: 0;">🔐</div>
            <span style="color: #1f2937; font-size: 15px; font-weight: 500;">Keep your new email address secure</span>
        </div>
        <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f8fafc; border-radius: 12px;">
            <div style="background: #3b82f6; color: white; width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 14px; flex-shrink: 0;">📱</div>
            <span style="color: #1f2937; font-size: 15px; font-weight: 500;">Ensure two-factor authentication is enabled</span>
        </div>
        <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f8fafc; border-radius: 12px;">
            <div style="background: #f59e0b; color: white; width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 14px; flex-shrink: 0;">🔍</div>
            <span style="color: #1f2937; font-size: 15px; font-weight: 500;">Regularly review your account activity</span>
        </div>
        <div style="display: flex; align-items: center; gap: 12px; padding: 16px; background: #f8fafc; border-radius: 12px;">
            <div style="background: #8b5cf6; color: white; width: 32px; height: 32px; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 14px; flex-shrink: 0;">📧</div>
            <span style="color: #1f2937; font-size: 15px; font-weight: 500;">Update your email address with other services if needed</span>
        </div>
    </div>
</div>

<!-- Help Section -->
<div style="background: linear-gradient(135deg, #eff6ff, #dbeafe); border: 2px solid #3b82f6; border-radius: 16px; padding: 32px; margin: 40px 0; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);"></div>
    <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px;">
        <div style="background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; width: 64px; height: 64px; border-radius: 20px; display: flex; align-items: center; justify-content: center; font-size: 28px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);">🤝</div>
        <div>
            <h3 style="color: #1e40af; margin: 0 0 8px 0; font-size: 20px; font-weight: 700;">Need Help?</h3>
            <p style="color: #2563eb; margin: 0; font-size: 16px; font-weight: 500;">Questions about this change? We're here to help</p>
        </div>
    </div>
    <div style="background: #ffffff; border: 1px solid #bfdbfe; border-radius: 12px; padding: 20px; text-align: center;">
        <div style="font-size: 24px; margin-bottom: 8px;">📧</div>
        <strong style="color: #1e40af; font-size: 16px;">Contact Support</strong>
        <p style="margin: 8px 0 0 0; font-size: 14px;"><a href="mailto:{{supportEmail}}" style="color: #2563eb; text-decoration: none; font-weight: 600;">{{supportEmail}}</a></p>
    </div>
</div>

<!-- Welcome Footer -->
<div style="text-align: center; margin: 48px 0; padding: 40px; background: linear-gradient(135deg, #f8fafc, #f1f5f9); border: 2px solid #e2e8f0; border-radius: 20px; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #ff6b35, #f7931e, #e97317);"></div>
    <div style="background: rgba(255, 107, 53, 0.1); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center; border: 2px solid rgba(255, 107, 53, 0.2);">
        <span style="font-size: 36px;">✅</span>
    </div>
    <h3 style="margin: 0 0 12px 0; font-size: 24px; color: #1f2937; font-weight: 700;">Email Successfully Updated</h3>
    <p style="margin: 0 0 24px 0; color: #6b7280; font-size: 18px; font-weight: 500;">Your account is secure and ready to use with your new email address.</p>
    <div style="background: #ffffff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; display: inline-block;">
        <p style="margin: 0 0 8px 0; font-size: 18px; color: #1f2937; font-weight: 600;">Best regards,</p>
        <p style="margin: 0; color: #6b7280; font-size: 16px;"><strong>The {{siteName}} Team</strong></p>
    </div>
</div>
