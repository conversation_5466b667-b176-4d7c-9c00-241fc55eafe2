<h1 style="color: #2563eb; margin-bottom: 24px; font-size: 28px;">You have been promoted to the Developer role on RSGlider</h1>

<p style="font-size: 16px; line-height: 1.6; margin-bottom: 24px;">Hi {{#if name}}{{name}}{{else}}Developer{{/if}},</p>

<p style="font-size: 16px; line-height: 1.6; margin-bottom: 24px;">Congratulations! You've been promoted to <strong>Developer</strong> status in the RSGlider community. This opens up exciting new opportunities for you to contribute and earn from your automation scripts.</p>

<div style="background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 24px; border-radius: 12px; margin: 32px 0; text-align: center;">
    <h2 style="margin: 0 0 8px 0; font-size: 24px;">🎉 Developer Status Activated!</h2>
    <p style="margin: 0; font-size: 16px; opacity: 0.9;">Your account now has full developer privileges and source control access.</p>
</div>

<h2 style="color: #1f2937; margin: 32px 0 16px 0; font-size: 24px;">Your New Developer Abilities</h2>

<div style="background: #ffffff; border: 1px solid #e5e7eb; border-left: 4px solid #2563eb; padding: 24px; margin: 24px 0; border-radius: 0 8px 8px 0;">
    <div style="display: grid; gap: 16px;">
        <div style="display: flex; align-items: flex-start; gap: 12px;">
            <span style="background: #2563eb; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; flex-shrink: 0; margin-top: 2px;">🛠️</span>
            <div>
                <strong style="color: #1f2937;">Create & Manage Repositories</strong><br>
                <span style="color: #6b7280;">Build automation scripts and tools with full version control</span>
            </div>
        </div>

        <div style="display: flex; align-items: flex-start; gap: 12px;">
            <span style="background: #059669; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; flex-shrink: 0; margin-top: 2px;">📦</span>
            <div>
                <strong style="color: #1f2937;">Publish to Marketplace</strong><br>
                <span style="color: #6b7280;">Share your scripts with the RSGlider community</span>
            </div>
        </div>

        <div style="display: flex; align-items: flex-start; gap: 12px;">
            <span style="background: #dc2626; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; flex-shrink: 0; margin-top: 2px;">💰</span>
            <div>
                <strong style="color: #1f2937;">Earn Revenue</strong><br>
                <span style="color: #6b7280;">Get paid for popular scripts and contributions</span>
            </div>
        </div>

        <div style="display: flex; align-items: flex-start; gap: 12px;">
            <span style="background: #7c3aed; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; flex-shrink: 0; margin-top: 2px;">👥</span>
            <div>
                <strong style="color: #1f2937;">Collaborate with Other Developers</strong><br>
                <span style="color: #6b7280;">Work together on projects and share knowledge</span>
            </div>
        </div>
    </div>
</div>

<div style="text-align: center; margin: 40px 0;">
    <a href="{{giteaUrl}}" style="display: inline-block; background: linear-gradient(135deg, #2563eb, #1d4ed8); color: #ffffff !important; padding: 16px 32px; border-radius: 8px; text-decoration: none; font-weight: 600; font-size: 16px; border: none; cursor: pointer; box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);">
        Access Your Development Environment
    </a>
</div>

<div style="background: #ffffff; border: 2px solid #bfdbfe; padding: 24px; border-radius: 8px; margin: 32px 0;">
    <h3 style="color: #1e40af; margin: 0 0 16px 0; font-size: 18px;">🔐 Your Development Access</h3>
    <div style="background: #f8f9fa; border: 1px solid #e9ecef; padding: 16px; border-radius: 6px; font-family: monospace; font-size: 14px; color: #333333;">
        <strong>Username:</strong> {{giteaUsername}}<br>
        <strong>Access:</strong> Automatic login when signed into RSGlider<br>
        <strong>Security:</strong> Synced with your RSGlider account
    </div>
    <p style="margin: 16px 0 0 0; color: #1e40af; font-size: 14px;">
        <strong>Note:</strong> Your development environment is automatically synced with your RSGlider account. Use your RSGlider credentials - no separate password needed!
    </p>
</div>

<h2 style="color: #1f2937; margin: 32px 0 16px 0; font-size: 24px;">Next Steps</h2>

<div style="background: #ffffff; border: 1px solid #e5e7eb; padding: 24px; border-radius: 8px; margin: 24px 0;">
    <ol style="margin: 0; padding-left: 20px; color: #374151;">
        <li style="margin-bottom: 12px;"><strong>Explore your development environment</strong> - Familiarize yourself with the tools</li>
        <li style="margin-bottom: 12px;"><strong>Create your first repository</strong> - Start building automation scripts</li>
        <li style="margin-bottom: 12px;"><strong>Review marketplace guidelines</strong> - Learn our publishing standards</li>
        <li style="margin-bottom: 12px;"><strong>Join the developer community</strong> - Connect with other RSGlider developers</li>
        <li style="margin-bottom: 0;"><strong>Set up payouts</strong> - Configure your payment preferences</li>
    </ol>
</div>

<h2 style="color: #1f2937; margin: 32px 0 16px 0; font-size: 24px;">Earning Opportunities</h2>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin: 24px 0;">
    <div style="background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 20px; border-radius: 8px; text-align: center;">
        <div style="font-size: 24px; margin-bottom: 8px;">💵</div>
        <strong>Script Sales</strong><br>
        <small style="opacity: 0.9;">Direct marketplace revenue</small>
    </div>
    <div style="background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; padding: 20px; border-radius: 8px; text-align: center;">
        <div style="font-size: 24px; margin-bottom: 8px;">🔄</div>
        <strong>Subscriptions</strong><br>
        <small style="opacity: 0.9;">Recurring premium tools</small>
    </div>
</div>

<div style="background: #ffffff; border: 1px solid #f59e0b; border-left: 4px solid #f59e0b; padding: 20px; margin: 32px 0; border-radius: 0 8px 8px 0;">
    <h3 style="color: #92400e; margin: 0 0 12px 0; font-size: 18px;">Need Help?</h3>
    <p style="margin: 0; color: #92400e;">
        Our developer support team is here to help you succeed:<br>
        📧 <a href="mailto:<EMAIL>" style="color: #92400e;"><EMAIL></a><br>
        📚 <a href="{{baseUrl}}/docs" style="color: #92400e;">RSGlider Documentation</a>
    </p>
</div>

<div style="text-align: center; margin: 40px 0; padding: 32px; background: #ffffff; border: 2px solid #e5e7eb; border-radius: 12px;">
    <p style="margin: 0 0 8px 0; font-size: 18px; color: #374151;">Welcome to the team!</p>
    <p style="margin: 0; color: #6b7280;">We're excited to see what amazing automation tools you'll create.</p>
</div>

<p style="font-size: 16px; color: #374151; margin: 24px 0 8px 0;">Happy coding!</p>
<p style="font-size: 16px; color: #374151; margin: 0;"><strong>The RSGlider Developer Team</strong></p>
