<h2>Welcome to the RSGlider Developer Program! 🚀</h2>

<p>Hi {{#if name}}{{name}}{{else}}Developer{{/if}},</p>

<p>Congratulations! You've been promoted to <strong>Developer</strong> status in the RSGlider community. This opens up exciting new opportunities for you to contribute and earn from your automation scripts.</p>

<div class="alert alert-success">
    <strong>🎉 Developer Status Activated!</strong><br>
    Your account now has full developer privileges and Gitea access.
</div>

<h3>🔧 Your New Developer Abilities</h3>
<p>As a RSGlider Developer, you can now:</p>
<ul>
    <li>🛠️ <strong>Create & Manage Repositories</strong> - Build automation scripts and tools</li>
    <li>📦 <strong>Publish to Marketplace</strong> - Share your scripts with the community</li>
    <li>💰 <strong>Earn Revenue</strong> - Get paid for popular scripts and contributions</li>
    <li>🔐 <strong>Access Gitea Platform</strong> - Full version control and collaboration tools</li>
    <li>👥 <strong>Collaborate</strong> - Work with other developers on projects</li>
    <li>📊 <strong>Analytics Dashboard</strong> - Track your script performance and earnings</li>
</ul>

<h3>🚀 Getting Started</h3>
<div style="text-align: center; margin: 30px 0;">
    {{> button url=giteaUrl text="Access Your Gitea Account"}}
</div>

<p><strong>Your Gitea Credentials:</strong></p>
<div class="code-block">
    <strong>Username:</strong> {{giteaUsername}}<br>
    <strong>URL:</strong> {{giteaUrl}}<br>
    <strong>SSO:</strong> Automatic login when signed into RSGlider
</div>

<div class="alert alert-info">
    <strong>🔒 Security Note:</strong> Your Gitea account is automatically synced with your RSGlider account. Use your RSGlider credentials to access Gitea - no separate password needed!
</div>

<h3>💡 Next Steps</h3>
<ol>
    <li><strong>Explore Gitea</strong> - Familiarize yourself with the development environment</li>
    <li><strong>Create Your First Repository</strong> - Start building automation scripts</li>
    <li><strong>Review Marketplace Guidelines</strong> - Learn our publishing standards</li>
    <li><strong>Join Developer Community</strong> - Connect with other RSGlider developers</li>
    <li><strong>Set Up Payouts</strong> - Configure your payment preferences</li>
</ol>

<h3>📚 Developer Resources</h3>
<ul>
    <li>📖 <a href="{{baseUrl}}/docs/developer-guide">Developer Documentation</a></li>
    <li>🎯 <a href="{{baseUrl}}/docs/marketplace">Marketplace Guidelines</a></li>
    <li>💬 <a href="{{baseUrl}}/community/developers">Developer Community</a></li>
    <li>🎓 <a href="{{baseUrl}}/docs/tutorials">Script Development Tutorials</a></li>
</ul>

<h3>💰 Earning Potential</h3>
<p>Developers can earn through:</p>
<ul>
    <li>💵 <strong>Script Sales</strong> - Direct revenue from marketplace purchases</li>
    <li>🔄 <strong>Subscription Scripts</strong> - Recurring revenue from premium tools</li>
    <li>🏆 <strong>Bounties</strong> - Complete community-requested features</li>
    <li>🤝 <strong>Collaborations</strong> - Partner with other developers</li>
</ul>

<h3>Need Help?</h3>
<p>Our developer support team is here to help you succeed:</p>
<ul>
    <li>📧 Email: <a href="mailto:<EMAIL>"><EMAIL></a></li>
    <li>💬 Developer Discord: <a href="{{discordUrl}}">Join our community</a></li>
    <li>📚 Documentation: <a href="{{baseUrl}}/docs">RSGlider Docs</a></li>
</ul>

<p>Welcome to the team! We're excited to see what amazing automation tools you'll create.</p>

<p>Happy coding!</p>
<p><strong>The RSGlider Developer Team</strong></p>
