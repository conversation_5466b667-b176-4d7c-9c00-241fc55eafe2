import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsObject, IsOptional, IsString } from 'class-validator';

export class SendTestEmailDto {
  @ApiProperty({
    description: 'Email address to send test email to',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  to?: string;

  @ApiProperty({
    description: 'Subject line for the test email',
    example: 'Test Email from RSGlider',
    required: false,
  })
  @IsOptional()
  @IsString()
  subject?: string;

  @ApiProperty({
    description: 'Email template to use for the test',
    example: 'test',
    required: false,
  })
  @IsOptional()
  @IsString()
  template?: string;

  @ApiProperty({
    description: 'Context variables to pass to the email template',
    example: { message: 'This is a test email', userName: 'John Doe' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  context?: Record<string, any>;
}
