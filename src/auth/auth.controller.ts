import { Body, Controller, HttpCode, HttpStatus, Post, Req, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import type { Request } from 'express';
import { CurrentUser } from '../common/decorators/current-user.decorator.js';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard.js';
import type { User } from '../database/schema/users.schema.js';
import { AuthService } from './auth.service.js';
import { AuthResponse } from './dto/auth-response.dto.js';
import { LoginRequest } from './dto/login-request.dto.js';
import { RegisterRequest } from './dto/register-request.dto.js';
// import { MailerService } from '@nestjs-modules/mailer';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    // private readonly mailerService: MailerService,
  ) { }

  @Post('/register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({ status: 201, description: 'User registered successfully', type: AuthResponse })
  @ApiResponse({ status: 409, description: 'User already exists' })
  async registerUser(@Body() registerData: RegisterRequest): Promise<AuthResponse> {
    return this.authService.registerUser(registerData);
  }

  @Post('/login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Login user' })
  @ApiResponse({ status: 200, description: 'Login successful', type: AuthResponse })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async loginUser(@Body() loginData: LoginRequest): Promise<AuthResponse> {
    return this.authService.loginUser(loginData);
  }

  @Post('/logout')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Logout user (invalidate current session)' })
  @ApiResponse({ status: 200, description: 'Success' })
  async logoutUser(@CurrentUser() user: User, @Req() req: Request) {
    return this.authService.logoutUser(user, req);
  }

  @Post('/refresh')
  @ApiOperation({ summary: 'Refresh access token using refresh token' })
  @ApiResponse({ status: 200, description: 'Success' })
  async refreshToken(@Body() body: any) {
    return this.authService.refreshToken(body);
  }

  @Post('/forgot-password')
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({ status: 200, description: 'Success' })
  async forgotPassword(@Body() body: any) {
    return this.authService.forgotPassword(body);
  }

  @Post('/reset-password')
  @ApiOperation({ summary: 'Reset password with token' })
  @ApiResponse({ status: 200, description: 'Success' })
  async resetPassword(@Body() body: any) {
    return this.authService.resetPassword(body);
  }


}
