import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { CommonModule } from '../common/common.module.js';
import { DatabaseModule } from '../database/database.module.js';
import { AuthController } from './auth.controller.js';
import { AuthService } from './auth.service.js';
import { JwtStrategy } from './strategies/jwt.strategy.js';

@Module({
  imports: [
    DatabaseModule,
    PassportModule,
    CommonModule,
    EmailModule,
    // MailerModule.forRootAsync({
    //   useFactory: (configService: ConfigService) => ({
    //     transport: {
    //       host: configService.get('SMTP_HOST'),
    //       port: parseInt(configService.get('SMTP_PORT', '587'), 10),
    //       secure: configService.get('SMTP_SECURE') === 'true',
    //       auth: {
    //         user: configService.get('SMTP_USER'),
    //         pass: configService.get('SMTP_PASS'),
    //       },
    //     },
    //     defaults: {
    //       from: configService.get('SMTP_FROM', 'RSGlider <<EMAIL>>'),
    //     },
    //   }),
    //   inject: [ConfigService],
    // }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '15m'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy],
  exports: [AuthService],
})
export class AuthModule { }
