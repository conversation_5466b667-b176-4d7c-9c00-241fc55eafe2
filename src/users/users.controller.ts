import { Body, Controller, Delete, ForbiddenException, Get, Param, Post, Put, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import type { Request } from 'express';
import { CurrentUser } from '../common/decorators/current-user.decorator.js';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard.js';
import type { User } from '../database/schema/users.schema.js';
import { CreateDeviceRequestDto } from './dto/create-device-request.dto.js';
import { DeviceDto } from './dto/device.dto.js';
import { SessionDto } from './dto/session.dto.js';
import { TwoFactorDisableRequest } from './dto/two-factor-disable-request.dto.js';
import { TwoFactorEnabledResponse } from './dto/two-factor-enabled-response.dto.js';
import { TwoFactorSetupResponse } from './dto/two-factor-setup-response.dto.js';
import { TwoFactorVerifyRequest } from './dto/two-factor-verify-request.dto.js';
import { UpdateUserRequest } from './dto/update-user-request.dto.js';
import { User as UserDto } from './dto/user.dto.js';
import { UsersService } from './users.service.js';

@ApiTags('User Profile')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UsersController {
  constructor(private readonly usersService: UsersService) { }

  @Get('/me')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'Success', type: UserDto })
  async getCurrentUser(@CurrentUser() user: User): Promise<UserDto> {
    return this.usersService.getCurrentUser(user.id);
  }

  @Put('/me')
  @ApiOperation({ summary: 'Update current user profile' })
  @ApiResponse({ status: 200, description: 'Success', type: UserDto })
  async updateCurrentUser(
    @CurrentUser() user: User,
    @Body() updateData: UpdateUserRequest
  ): Promise<UserDto> {
    // If email is being updated, use updateProfile method which handles email verification
    if (updateData.email) {
      const { firstName, lastName, email } = updateData;
      const name = firstName && lastName ? `${firstName} ${lastName}` : undefined;
      return this.usersService.updateProfile(user.id, { name, email });
    }

    // Otherwise use the regular updateCurrentUser method
    return this.usersService.updateCurrentUser(user.id, updateData);
  }

  // 2FA Management Endpoints

  @Post('/me/2fa/setup')
  @ApiOperation({ summary: 'Setup two-factor authentication' })
  @ApiResponse({ status: 200, description: '2FA setup initiated', type: TwoFactorSetupResponse })
  @ApiResponse({ status: 400, description: '2FA already enabled' })
  async setup2FA(@CurrentUser() user: User): Promise<TwoFactorSetupResponse> {
    return this.usersService.setup2FA(user.id);
  }

  @Post('/me/2fa/verify-setup')
  @ApiOperation({ summary: 'Verify and enable two-factor authentication' })
  @ApiResponse({ status: 200, description: '2FA enabled successfully', type: TwoFactorEnabledResponse })
  @ApiResponse({ status: 400, description: 'Invalid TOTP code or setup not initiated' })
  async verifySetup2FA(
    @CurrentUser() user: User,
    @Body() verifyData: TwoFactorVerifyRequest
  ): Promise<TwoFactorEnabledResponse> {
    return this.usersService.verifySetup2FA(user.id, verifyData.code);
  }

  @Post('/me/2fa/disable')
  @ApiOperation({ summary: 'Disable two-factor authentication' })
  @ApiResponse({ status: 200, description: '2FA disabled successfully' })
  @ApiResponse({ status: 400, description: 'Invalid credentials or verification code' })
  async disable2FA(
    @CurrentUser() user: User,
    @Body() disableData: TwoFactorDisableRequest
  ): Promise<{ message: string }> {
    return this.usersService.disable2FA(user.id, disableData.password, disableData.code);
  }

  // Device Management Endpoints

  @Get('/me/devices')
  @ApiOperation({ summary: 'Get registered devices' })
  @ApiResponse({ status: 200, description: 'List of devices', type: [DeviceDto] })
  async getDevices(@CurrentUser() user: User): Promise<DeviceDto[]> {
    return this.usersService.getUserDevices(user.id);
  }

  @Post('/me/devices')
  @ApiOperation({ summary: 'Register new device' })
  @ApiResponse({ status: 201, description: 'Device registered', type: DeviceDto })
  async registerDevice(
    @CurrentUser() user: User,
    @Body() dto: CreateDeviceRequestDto
  ): Promise<DeviceDto> {
    return this.usersService.registerDevice(user.id, dto);
  }

  @Delete('/me/devices/:deviceId')
  @ApiOperation({ summary: 'Remove device' })
  @ApiResponse({ status: 200, description: 'Device removed' })
  async removeDevice(
    @CurrentUser() user: User,
    @Param('deviceId') deviceId: string,
    @Req() req: Request
  ): Promise<{ message: string }> {
    return this.usersService.removeDevice(user.id, deviceId, req);
  }

  // Session Management Endpoints

  @Get('/me/sessions')
  @ApiOperation({ summary: 'Get active sessions' })
  @ApiResponse({ status: 200, description: 'List of sessions', type: [SessionDto] })
  async getSessions(@CurrentUser() user: User): Promise<SessionDto[]> {
    return this.usersService.getUserSessions(user.id);
  }

  @Delete('/me/sessions/:sessionId')
  @ApiOperation({ summary: 'Remove a session' })
  @ApiResponse({ status: 200, description: 'Session removed' })
  async removeSession(
    @CurrentUser() user: User,
    @Param('sessionId') sessionId: string,
    @Req() req: Request
  ): Promise<{ message: string }> {
    if (!user || (!user.roles?.includes('admin') && !user.id)) {
      throw new ForbiddenException('Not authorized');
    }
    return this.usersService.removeUserSession(user.id, sessionId, req);
  }

  @Delete('/me/sessions')
  @ApiOperation({ summary: 'Remove all sessions except current' })
  @ApiResponse({ status: 200, description: 'All other sessions removed' })
  async removeAllSessions(@CurrentUser() user: User, @Req() req: Request): Promise<{ message: string }> {
    if (!user || (!user.roles?.includes('admin') && !user.id)) {
      throw new ForbiddenException('Not authorized');
    }
    return this.usersService.removeAllUserSessions(user.id, req);
  }
}
