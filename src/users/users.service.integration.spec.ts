/**
 * UsersService Integration Tests
 * Tests the UsersService with real database operations
 */

import { BadRequestException, ConflictException, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { and, eq } from 'drizzle-orm';
import {
  cleanTestDatabase,
  closeTestDatabase,
  createTestDatabase,
  seedTestDatabase,
  TestDatabase
} from '../../test/database-setup.js';
import { userRoles, users, userSessions } from '../database/schema/index.js';
import { UsersService } from './users.service.js';

describe('UsersService Integration Tests', () => {
  let service: UsersService;
  let testDb: TestDatabase;
  let testData: any;

  beforeAll(async () => {
    testDb = await createTestDatabase();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: 'DB',
          useValue: testDb,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key, defaultValue) => {
              const config = {
                'JWT_SECRET': 'test-secret',
                'JWT_EXPIRES_IN': '15m',
                'JWT_REFRESH_SECRET': 'test-refresh-secret',
                'JWT_REFRESH_EXPIRES_IN': '7d',
              };
              return config[key] || defaultValue;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
  });

  beforeEach(async () => {
    await cleanTestDatabase(testDb);
    testData = await seedTestDatabase(testDb);
  });

  afterAll(async () => {
    await closeTestDatabase();
  });

  describe('getCurrentUser', () => {
    it('should get current user by ID', async () => {
      const result = await service.getCurrentUser(testData.testUser.id);

      expect(result).toMatchObject({
        id: testData.testUser.id,
        email: testData.testUser.email,
        firstName: expect.any(String),
        roles: ['user'],
        emailVerified: true,
        status: 'active',
      });
      expect(result.createdAt).toBeTruthy();
      expect(result.updatedAt).toBeTruthy();
    });

    it('should throw NotFoundException for non-existent user', async () => {
      const nonExistentId = 'e0eebc99-9c0b-4ef8-bb6d-6bb9bd380a55';

      await expect(service.getCurrentUser(nonExistentId)).rejects.toThrow(NotFoundException);
      await expect(service.getCurrentUser(nonExistentId)).rejects.toThrow('User not found');
    });
  });

  describe('findByEmail', () => {
    it('should find user by email', async () => {
      const result = await service.findByEmail(testData.testUser.email);

      expect(result).toMatchObject({
        id: testData.testUser.id,
        email: testData.testUser.email,
        firstName: 'Test',
        lastName: 'User',
        roles: ['user'],
        emailVerified: true,
      });
    });

    it('should return null for non-existent email', async () => {
      const result = await service.findByEmail('<EMAIL>');
      expect(result).toBeNull();
    });
  });

  describe('updateProfile', () => {
    it('should update user profile successfully', async () => {
      const updateData = {
        name: 'Updated Name',
        bio: 'Updated bio',
      };

      const result = await service.updateProfile(testData.testUser.id, updateData);

      expect(result).toMatchObject({
        id: testData.testUser.id,
        email: testData.testUser.email,
        firstName: 'Updated',
        lastName: 'Name',
        bio: 'Updated bio',
      });

      // Verify in database
      const [updatedUser] = await testDb
        .select()
        .from(users)
        .where(eq(users.id, testData.testUser.id))
        .limit(1);

      expect(updatedUser.name).toBe('Updated Name');
      expect(updatedUser.bio).toBe('Updated bio');
    });

    it('should throw NotFoundException for non-existent user', async () => {
      const nonExistentId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';
      const updateData = { name: 'New Name' };

      await expect(service.updateProfile(nonExistentId, updateData)).rejects.toThrow(NotFoundException);
    });

    it('should throw ConflictException for duplicate email', async () => {
      const updateData = {
        email: testData.adminUser.email, // Try to use admin's email
      };

      await expect(
        service.updateProfile(testData.testUser.id, updateData)
      ).rejects.toThrow(ConflictException);
      await expect(
        service.updateProfile(testData.testUser.id, updateData)
      ).rejects.toThrow('Email already exists');
    });
  });

  describe('changePassword', () => {
    it('should change password successfully', async () => {
      const passwordData = {
        currentPassword: 'password123',
        newPassword: 'NewSecurePassword456!',
      };

      const result = await service.changePassword(testData.testUser.id, passwordData);

      expect(result).toEqual({ message: 'Password changed successfully' });

      // Verify password was changed in database
      const [updatedUser] = await testDb
        .select()
        .from(users)
        .where(eq(users.id, testData.testUser.id))
        .limit(1);

      expect(updatedUser.password).not.toBe(testData.testUser.password);
      expect(updatedUser.password).toMatch(/^\$2b\$12\$/); // bcrypt hash format
    });

    it('should throw BadRequestException for incorrect current password', async () => {
      const passwordData = {
        currentPassword: 'wrongpassword',
        newPassword: 'NewSecurePassword456!',
      };

      await expect(
        service.changePassword(testData.testUser.id, passwordData)
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.changePassword(testData.testUser.id, passwordData)
      ).rejects.toThrow('Current password is incorrect');
    });

    it('should throw NotFoundException for non-existent user', async () => {
      const nonExistentId = 'g0eebc99-9c0b-4ef8-bb6d-6bb9bd380a77';
      const passwordData = {
        currentPassword: 'password123',
        newPassword: 'NewSecurePassword456!',
      };

      await expect(
        service.changePassword(nonExistentId, passwordData)
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('deactivateUser', () => {
    it('should deactivate user successfully', async () => {
      const result = await service.deactivateUser(testData.testUser.id);

      expect(result).toEqual({ message: 'User deactivated successfully' });

      // Verify user was deactivated in database
      const [deactivatedUser] = await testDb
        .select()
        .from(users)
        .where(eq(users.id, testData.testUser.id))
        .limit(1);

      expect(deactivatedUser.isActive).toBe(false);
    });

    it('should throw NotFoundException for non-existent user', async () => {
      const nonExistentId = 'h0eebc99-9c0b-4ef8-bb6d-6bb9bd380a88';

      await expect(service.deactivateUser(nonExistentId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('getUserRoles', () => {
    it('should return user roles', async () => {
      const result = await service.getUserRoles(testData.testUser.id);

      expect(result).toEqual(['user']);
    });

    it('should return admin roles', async () => {
      const result = await service.getUserRoles(testData.adminUser.id);

      expect(result).toEqual(expect.arrayContaining(['user', 'admin']));
    });

    it('should throw NotFoundException for non-existent user', async () => {
      const nonExistentId = 'i0eebc99-9c0b-4ef8-bb6d-6bb9bd380a99';

      await expect(service.getUserRoles(nonExistentId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('assignRole', () => {
    it('should assign role to user successfully', async () => {
      const result = await service.assignRole(testData.testUser.id, testData.adminRole.id);

      expect(result).toEqual({ message: 'Role assigned successfully' });

      // Verify role was assigned in database
      const userRoleRecords = await testDb
        .select()
        .from(userRoles)
        .where(eq(userRoles.userId, testData.testUser.id));

      const roleIds = userRoleRecords.map(ur => ur.roleId);
      expect(roleIds).toContain(testData.adminRole.id);
    });

    it('should throw NotFoundException for non-existent user', async () => {
      const nonExistentId = 'j0eebc99-9c0b-4ef8-bb6d-6bb9bd380aaa';

      await expect(
        service.assignRole(nonExistentId, testData.adminRole.id)
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw ConflictException for already assigned role', async () => {
      // Assign role first
      await service.assignRole(testData.testUser.id, testData.adminRole.id);

      // Try to assign same role again
      await expect(
        service.assignRole(testData.testUser.id, testData.adminRole.id)
      ).rejects.toThrow(ConflictException);
      await expect(
        service.assignRole(testData.testUser.id, testData.adminRole.id)
      ).rejects.toThrow('User already has this role');
    });
  });

  describe('removeRole', () => {
    beforeEach(async () => {
      // Assign admin role to test user for removal tests
      await service.assignRole(testData.testUser.id, testData.adminRole.id);
    });

    it('should remove role from user successfully', async () => {
      const result = await service.removeRole(testData.testUser.id, testData.adminRole.id);

      expect(result).toEqual({ message: 'Role removed successfully' });

      // Verify role was removed from database
      const userRoleRecords = await testDb
        .select()
        .from(userRoles)
        .where(eq(userRoles.userId, testData.testUser.id));

      const roleIds = userRoleRecords.map(ur => ur.roleId);
      expect(roleIds).not.toContain(testData.adminRole.id);
    });

    it('should throw NotFoundException for non-existent user', async () => {
      const nonExistentId = 'k0eebc99-9c0b-4ef8-bb6d-6bb9bd380bbb';

      await expect(
        service.removeRole(nonExistentId, testData.adminRole.id)
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException for role not assigned to user', async () => {
      // Remove the role first
      await service.removeRole(testData.testUser.id, testData.adminRole.id);

      // Try to remove it again
      await expect(
        service.removeRole(testData.testUser.id, testData.adminRole.id)
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.removeRole(testData.testUser.id, testData.adminRole.id)
      ).rejects.toThrow('User does not have this role');
    });
  });

  describe('updateCurrentUser', () => {
    it('should update current user successfully', async () => {
      const updateData = {
        firstName: 'NewFirst',
        lastName: 'NewLast',
        avatar: 'avatar-url',
      };

      const result = await service.updateCurrentUser(testData.testUser.id, updateData);

      expect(result).toMatchObject({
        id: testData.testUser.id,
        firstName: 'NewFirst',
        lastName: 'NewLast',
        avatar: 'avatar-url',
      });

      // Verify in database
      const [updatedUser] = await testDb
        .select()
        .from(users)
        .where(eq(users.id, testData.testUser.id))
        .limit(1);

      expect(updatedUser.name).toBe('NewFirst NewLast');
    });

    it('should preserve existing name parts when not provided', async () => {
      // First set a name
      await testDb
        .update(users)
        .set({ name: 'Existing First Existing Last' })
        .where(eq(users.id, testData.testUser.id));

      const updateData = {
        firstName: 'NewFirst',
        // lastName not provided - should preserve existing
      };

      const result = await service.updateCurrentUser(testData.testUser.id, updateData);

      expect(result).toMatchObject({
        firstName: 'NewFirst',
        lastName: 'First Existing Last', // Should preserve existing last name parts
      });
    });

    it('should handle empty/whitespace names correctly', async () => {
      const updateData = {
        firstName: '   ',
        lastName: '',
      };

      const result = await service.updateCurrentUser(testData.testUser.id, updateData);

      // Should preserve existing name when provided values are empty/whitespace
      expect(result.firstName).toBeTruthy();
      expect(result.lastName).toBeTruthy();
    });

    it('should throw NotFoundException when user not found', async () => {
      const nonExistentUserId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';
      const updateData = { firstName: 'New' };

      await expect(service.updateCurrentUser(nonExistentUserId, updateData)).rejects.toThrow(NotFoundException);
      await expect(service.updateCurrentUser(nonExistentUserId, updateData)).rejects.toThrow('User not found');
    });
  });

  describe('2FA Methods', () => {
    describe('setup2FA', () => {
      it('should setup 2FA successfully', async () => {
        const result = await service.setup2FA(testData.testUser.id);

        expect(result).toMatchObject({
          secret: expect.any(String),
          qrCodeUrl: expect.stringMatching(/^data:image\/png;base64,/),
          manualEntryKey: expect.any(String),
          issuer: 'RSGlider',
        });

        // Verify secret was stored in database
        const [updatedUser] = await testDb
          .select({ twoFactorSecret: users.twoFactorSecret })
          .from(users)
          .where(eq(users.id, testData.testUser.id))
          .limit(1);

        expect(updatedUser.twoFactorSecret).toBeTruthy();
      });

      it('should throw NotFoundException when user not found', async () => {
        const nonExistentUserId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';

        await expect(service.setup2FA(nonExistentUserId)).rejects.toThrow(NotFoundException);
        await expect(service.setup2FA(nonExistentUserId)).rejects.toThrow('User not found');
      });

      it('should throw BadRequestException when 2FA already enabled', async () => {
        // Enable 2FA first
        await testDb
          .update(users)
          .set({ twoFactorEnabled: true })
          .where(eq(users.id, testData.testUser.id));

        await expect(service.setup2FA(testData.testUser.id)).rejects.toThrow(BadRequestException);
        await expect(service.setup2FA(testData.testUser.id)).rejects.toThrow('Two-factor authentication is already enabled');
      });
    });

    describe('verifySetup2FA', () => {
      beforeEach(async () => {
        // Setup 2FA first
        await service.setup2FA(testData.testUser.id);
      });

      it('should throw BadRequestException for invalid code', async () => {
        await expect(service.verifySetup2FA(testData.testUser.id, 'invalid')).rejects.toThrow(BadRequestException);
        await expect(service.verifySetup2FA(testData.testUser.id, 'invalid')).rejects.toThrow('Invalid verification code');
      });

      it('should throw NotFoundException when user not found', async () => {
        const nonExistentUserId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';

        await expect(service.verifySetup2FA(nonExistentUserId, '123456')).rejects.toThrow(NotFoundException);
        await expect(service.verifySetup2FA(nonExistentUserId, '123456')).rejects.toThrow('User not found');
      });

      it('should throw BadRequestException when 2FA already enabled', async () => {
        // Enable 2FA first
        await testDb
          .update(users)
          .set({ twoFactorEnabled: true })
          .where(eq(users.id, testData.testUser.id));

        await expect(service.verifySetup2FA(testData.testUser.id, '123456')).rejects.toThrow(BadRequestException);
        await expect(service.verifySetup2FA(testData.testUser.id, '123456')).rejects.toThrow('Two-factor authentication is already enabled');
      });

      it('should throw BadRequestException when setup not initiated', async () => {
        // Clear the secret
        await testDb
          .update(users)
          .set({ twoFactorSecret: null })
          .where(eq(users.id, testData.testUser.id));

        await expect(service.verifySetup2FA(testData.testUser.id, '123456')).rejects.toThrow(BadRequestException);
        await expect(service.verifySetup2FA(testData.testUser.id, '123456')).rejects.toThrow('2FA setup not initiated. Please call setup endpoint first.');
      });
    });

    describe('disable2FA', () => {
      beforeEach(async () => {
        // Enable 2FA and set password
        await testDb
          .update(users)
          .set({
            twoFactorEnabled: true,
            twoFactorSecret: 'test-secret',
            password: testData.testUser.password // Use the hashed password from test data
          })
          .where(eq(users.id, testData.testUser.id));
      });

      it('should disable 2FA successfully with correct password', async () => {
        const result = await service.disable2FA(testData.testUser.id, 'password123');

        expect(result).toEqual({ message: 'Two-factor authentication has been successfully disabled' });

        // Verify 2FA was disabled in database
        const [updatedUser] = await testDb
          .select({
            twoFactorEnabled: users.twoFactorEnabled,
            twoFactorSecret: users.twoFactorSecret
          })
          .from(users)
          .where(eq(users.id, testData.testUser.id))
          .limit(1);

        expect(updatedUser.twoFactorEnabled).toBe(false);
        expect(updatedUser.twoFactorSecret).toBeNull();
      });

      it('should throw NotFoundException when user not found', async () => {
        const nonExistentUserId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';

        await expect(service.disable2FA(nonExistentUserId, 'password123')).rejects.toThrow(NotFoundException);
        await expect(service.disable2FA(nonExistentUserId, 'password123')).rejects.toThrow('User not found');
      });

      it('should throw BadRequestException when 2FA not enabled', async () => {
        // Disable 2FA first
        await testDb
          .update(users)
          .set({ twoFactorEnabled: false })
          .where(eq(users.id, testData.testUser.id));

        await expect(service.disable2FA(testData.testUser.id, 'password123')).rejects.toThrow(BadRequestException);
        await expect(service.disable2FA(testData.testUser.id, 'password123')).rejects.toThrow('Two-factor authentication is not enabled');
      });

      it('should throw UnauthorizedException for invalid password', async () => {
        await expect(service.disable2FA(testData.testUser.id, 'wrongpassword')).rejects.toThrow(UnauthorizedException);
        await expect(service.disable2FA(testData.testUser.id, 'wrongpassword')).rejects.toThrow('Invalid password');
      });
    });
  });

  describe('Device Management', () => {
    describe('getUserDevices', () => {
      it('should return empty array when no devices', async () => {
        const result = await service.getUserDevices(testData.testUser.id);
        expect(result).toEqual([]);
      });

      it('should return user devices', async () => {
        // Create a device first
        const deviceData = {
          deviceName: 'Test Device',
          deviceType: 'desktop',
          platform: 'windows',
          deviceInfo: { browser: 'chrome' },
        };

        await service.registerDevice(testData.testUser.id, deviceData);

        const result = await service.getUserDevices(testData.testUser.id);

        expect(result).toHaveLength(1);
        expect(result[0]).toMatchObject({
          deviceName: 'Test Device',
          deviceType: 'desktop',
          platform: 'windows',
          isTrusted: false,
          registeredAt: expect.any(String),
          createdAt: expect.any(String),
          updatedAt: expect.any(String),
        });
      });
    });

    describe('registerDevice', () => {
      it('should register device successfully', async () => {
        const deviceData = {
          deviceName: 'Test Device',
          deviceType: 'mobile',
          platform: 'ios',
          deviceInfo: { model: 'iPhone 12' },
        };

        const result = await service.registerDevice(testData.testUser.id, deviceData);

        expect(result).toMatchObject({
          deviceName: 'Test Device',
          deviceType: 'mobile',
          platform: 'ios',
          deviceInfo: { model: 'iPhone 12' },
          isTrusted: false,
          registeredAt: expect.any(String),
          createdAt: expect.any(String),
          updatedAt: expect.any(String),
        });

        // Verify in database
        const devices = await service.getUserDevices(testData.testUser.id);
        expect(devices).toHaveLength(1);
      });
    });

    describe('removeDevice', () => {
      let deviceId: string;

      beforeEach(async () => {
        const deviceData = {
          deviceName: 'Test Device',
          deviceType: 'desktop',
          platform: 'windows',
          deviceInfo: {},
        };

        const device = await service.registerDevice(testData.testUser.id, deviceData);
        deviceId = device.id;
      });

      it('should remove device successfully', async () => {
        const result = await service.removeDevice(testData.testUser.id, deviceId);

        expect(result).toEqual({ message: 'Device removed' });

        // Verify device was removed
        const devices = await service.getUserDevices(testData.testUser.id);
        expect(devices).toHaveLength(0);
      });

      it('should throw NotFoundException for non-existent device', async () => {
        const nonExistentDeviceId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';

        await expect(service.removeDevice(testData.testUser.id, nonExistentDeviceId)).rejects.toThrow(NotFoundException);
        await expect(service.removeDevice(testData.testUser.id, nonExistentDeviceId)).rejects.toThrow('Device not found');
      });

      it('should not remove device belonging to another user', async () => {
        await expect(service.removeDevice(testData.adminUser.id, deviceId)).rejects.toThrow(NotFoundException);
      });
    });
  });

  describe('Session Management', () => {
    describe('getUserSessions', () => {
      it('should return empty array when no sessions', async () => {
        const result = await service.getUserSessions(testData.testUser.id);
        expect(result).toEqual([]);
      });

      it('should return only active sessions', async () => {
        const now = new Date();
        const futureDate = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours from now

        // Create active session
        await testDb.insert(userSessions).values({
          userId: testData.testUser.id,
          platform: 'web',
          isActive: true,
          isCurrent: false,
          expiresAt: futureDate,
          lastActivityAt: now,
        });

        // Create inactive session
        await testDb.insert(userSessions).values({
          userId: testData.testUser.id,
          platform: 'web',
          isActive: false,
          isCurrent: false,
          expiresAt: futureDate,
          lastActivityAt: now,
        });

        const result = await service.getUserSessions(testData.testUser.id);

        expect(result).toHaveLength(1);
        expect(result[0]).toMatchObject({
          platform: 'web',
          isActive: true,
        });
      });

      it('should filter out expired sessions', async () => {
        const now = new Date();
        const pastDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24 hours ago

        // Create expired session
        await testDb.insert(userSessions).values({
          userId: testData.testUser.id,
          platform: 'web',
          isActive: true,
          isCurrent: false,
          expiresAt: pastDate,
          lastActivityAt: now,
        });

        const result = await service.getUserSessions(testData.testUser.id);

        expect(result).toEqual([]);
      });
    });

    describe('removeUserSession', () => {
      let sessionId: string;

      beforeEach(async () => {
        const now = new Date();
        const futureDate = new Date(now.getTime() + 24 * 60 * 60 * 1000);

        const [session] = await testDb.insert(userSessions).values({
          userId: testData.testUser.id,
          platform: 'web',
          isActive: true,
          isCurrent: false,
          expiresAt: futureDate,
          lastActivityAt: now,
        }).returning();

        sessionId = session.id;
      });

      it('should remove session successfully', async () => {
        const result = await service.removeUserSession(testData.testUser.id, sessionId);

        expect(result).toEqual({ message: 'Session revoked' });

        // Verify session was revoked
        const [revokedSession] = await testDb
          .select()
          .from(userSessions)
          .where(eq(userSessions.id, sessionId))
          .limit(1);

        expect(revokedSession.isActive).toBe(false);
        expect(revokedSession.revokedAt).toBeTruthy();
        expect(revokedSession.revokedReason).toBe('User-initiated');
      });

      it('should throw NotFoundException for non-existent session', async () => {
        const nonExistentSessionId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';

        await expect(service.removeUserSession(testData.testUser.id, nonExistentSessionId)).rejects.toThrow(NotFoundException);
        await expect(service.removeUserSession(testData.testUser.id, nonExistentSessionId)).rejects.toThrow('Session not found');
      });

      it('should throw BadRequestException for current session', async () => {
        // Mark session as current
        await testDb
          .update(userSessions)
          .set({ isCurrent: true })
          .where(eq(userSessions.id, sessionId));

        await expect(service.removeUserSession(testData.testUser.id, sessionId)).rejects.toThrow(BadRequestException);
        await expect(service.removeUserSession(testData.testUser.id, sessionId)).rejects.toThrow('Cannot delete the current session');
      });

      it('should throw BadRequestException for already revoked session', async () => {
        // Revoke session first
        await testDb
          .update(userSessions)
          .set({ isActive: false })
          .where(eq(userSessions.id, sessionId));

        await expect(service.removeUserSession(testData.testUser.id, sessionId)).rejects.toThrow(BadRequestException);
        await expect(service.removeUserSession(testData.testUser.id, sessionId)).rejects.toThrow('Session already revoked');
      });
    });

    describe('removeAllUserSessions', () => {
      beforeEach(async () => {
        const now = new Date();
        const futureDate = new Date(now.getTime() + 24 * 60 * 60 * 1000);

        // Create multiple sessions
        await testDb.insert(userSessions).values([
          {
            userId: testData.testUser.id,
            platform: 'web',
            isActive: true,
            isCurrent: true,
            expiresAt: futureDate,
            lastActivityAt: now,
          },
          {
            userId: testData.testUser.id,
            platform: 'web',
            isActive: true,
            isCurrent: false,
            expiresAt: futureDate,
            lastActivityAt: now,
          },
          {
            userId: testData.testUser.id,
            platform: 'desktop',
            isActive: true,
            isCurrent: false,
            expiresAt: futureDate,
            lastActivityAt: now,
          },
        ]);
      });

      it('should revoke all sessions except current', async () => {
        const result = await service.removeAllUserSessions(testData.testUser.id);

        expect(result).toEqual({ message: 'All other sessions revoked' });

        // Verify only current session remains active
        const activeSessions = await testDb
          .select()
          .from(userSessions)
          .where(and(eq(userSessions.userId, testData.testUser.id), eq(userSessions.isActive, true)));

        expect(activeSessions).toHaveLength(1);
        expect(activeSessions[0].isCurrent).toBe(true);
      });
    });
  });
});
