import { pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';
import { permissions } from './permissions.schema.js';
import { roles } from './roles.schema.js';

// Named functions for better test coverage
export function getRoleIdReference() {
  return roles.id;
}

export function getPermissionIdReference() {
  return permissions.id;
}

export const rolePermissions = pgTable('role_permissions', {
  id: uuid('id').primaryKey().defaultRandom(),
  roleId: uuid('role_id').notNull().references(getRoleIdReference),
  permissionId: uuid('permission_id').notNull().references(getPermissionIdReference),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});