import { pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';
import { roles } from './roles.schema.js';
import { users } from './users.schema.js';

// Named functions for better test coverage
export function getUserIdReference() {
  return users.id;
}

export function getRoleIdReference() {
  return roles.id;
}

export const userRoles = pgTable('user_roles', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(getUserIdReference),
  roleId: uuid('role_id').notNull().references(getRoleIdReference),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});