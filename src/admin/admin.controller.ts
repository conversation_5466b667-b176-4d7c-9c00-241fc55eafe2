import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from '../common/decorators/roles.decorator.js';
import { UserRole } from '../common/enums/user-role.enum.js';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard.js';
import { RolesGuard } from '../common/guards/roles.guard.js';
import { DeveloperManagementService } from '../common/services/developer-management.service.js';
import {
    CreateEmailCampaignDto,
    CreateEmailTemplateDto,
    EmailCampaignQueryDto,
    EmailCampaignResponseDto,
    EmailLogQueryDto,
    EmailTemplateQueryDto,
    EmailTemplateResponseDto,
    SendEmailDto,
    UpdateEmailCampaignDto,
    UpdateEmailTemplateDto
} from '../email/dto/index.js';
import { EmailManagementService } from '../email/email-management.service.js';
import { EmailService } from '../email/email.service.js';
import { Role } from '../users/dto/role.dto.js';
import { SessionDto } from '../users/dto/session.dto.js';
import { User } from '../users/dto/user.dto.js';
import { UsersService } from '../users/users.service.js';
import { PermissionsService } from './permissions.service.js';
import { RolesService } from './roles.service.js';

@ApiTags('Admin')
@Controller('admin')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class AdminController {
  constructor(
    private readonly usersService: UsersService,
    private readonly rolesService: RolesService,
    private readonly permissionsService: PermissionsService,
    private readonly emailManagementService: EmailManagementService,
    private readonly emailService: EmailService,
    private readonly developerManagementService: DeveloperManagementService,
  ) { }

  // === USER MANAGEMENT ===

  @Get('users/:userId/sessions')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'List sessions for any user (admin only)' })
  @ApiResponse({ status: 200, description: 'List of sessions', type: [SessionDto] })
  async getUserSessions(@Param('userId') userId: string): Promise<SessionDto[]> {
    return this.usersService.getUserSessions(userId);
  }

  @Delete('users/:userId/sessions/:sessionId')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Revoke a session for any user (admin only)' })
  @ApiResponse({ status: 200, description: 'Session revoked' })
  async revokeUserSession(
    @Param('userId') userId: string,
    @Param('sessionId') sessionId: string,
  ): Promise<{ message: string }> {
    return this.usersService.removeUserSession(userId, sessionId);
  }

  @Get('users')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'List all users' })
  @ApiResponse({ status: 200, description: 'List of users', type: [User] })
  async listUsers(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
    @Query('search') search: string = '',
    @Query('status') status: string = '',
    @Query('role') role: string = '',
  ) {
    return this.usersService.adminListUsers({ page, limit, search, status, role });
  }

  @Post('users')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({ status: 201, description: 'User created', type: User })
  async createUser(@Body() body: any) {
    return this.usersService.adminCreateUser(body);
  }

  @Get('users/:userId')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get user details' })
  @ApiResponse({ status: 200, description: 'User details', type: User })
  async getUser(@Param('userId') userId: string) {
    return this.usersService.adminGetUser(userId);
  }

  @Put('users/:userId')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update user' })
  @ApiResponse({ status: 200, description: 'User updated', type: User })
  async updateUser(@Param('userId') userId: string, @Body() body: any) {
    return this.usersService.adminUpdateUser(userId, body);
  }

  @Delete('users/:userId')
  @Roles(UserRole.ADMIN)
  @HttpCode(204)
  @ApiOperation({ summary: 'Delete user' })
  @ApiResponse({ status: 204, description: 'User deleted' })
  async deleteUser(@Param('userId') userId: string) {
    await this.usersService.adminDeleteUser(userId);
  }

  @Post('users/:userId/promote-to-developer')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Promote user to developer with Gitea integration' })
  @ApiResponse({ status: 200, description: 'User promoted to developer successfully' })
  async promoteUserToDeveloper(
    @Param('userId') userId: string,
    @Body() body: { giteaUsername?: string; sendWelcomeEmail?: boolean }
  ) {
    // Get the user details
    const user = await this.usersService.adminGetUser(userId);

    // Find or create developer role
    const roles = await this.rolesService.listRoles();
    let developerRole = roles.find(role => role.name === 'developer');

    if (!developerRole) {
      developerRole = await this.rolesService.createRole({
        name: 'developer',
        description: 'Developer role with Gitea access and marketplace permissions',
        isSystemRole: true
      });
    }

    // Assign developer role to user
    await this.usersService.adminAssignRoleToUser(userId, developerRole.id);

    // Create developer profile and Gitea account
    const developerProfile = await this.developerManagementService.createDeveloper({
      userId,
      giteaUsername: body.giteaUsername,
      autoProvision: true
    });

    // Send welcome email if requested (default: true)
    if (body.sendWelcomeEmail !== false) {
      await this.emailService.sendDeveloperWelcome(
        user.email,
        user.name || 'Developer',
        developerProfile.giteaProfile?.giteaUsername || body.giteaUsername || 'developer'
      );
    }

    return {
      message: 'User promoted to developer successfully',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        roles: [...(user.roles || []), 'developer']
      },
      giteaProfile: developerProfile.giteaProfile,
      isProvisioned: developerProfile.isProvisioned
    };
  }

  // === ROLE MANAGEMENT ===

  @Get('roles')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'List all roles' })
  @ApiResponse({ status: 200, description: 'List of roles', type: [Role] })
  async listRoles() {
    return this.rolesService.listRoles();
  }

  @Post('roles')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new role' })
  @ApiResponse({ status: 201, description: 'Role created', type: Role })
  async createRole(@Body() body: any) {
    return this.rolesService.createRole(body);
  }

  @Get('roles/:roleId')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get role details' })
  @ApiResponse({ status: 200, description: 'Role details', type: Role })
  async getRole(@Param('roleId') roleId: string) {
    return this.rolesService.getRole(roleId);
  }

  @Put('roles/:roleId')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update role' })
  @ApiResponse({ status: 200, description: 'Role updated', type: Role })
  async updateRole(@Param('roleId') roleId: string, @Body() body: any) {
    return this.rolesService.updateRole(roleId, body);
  }

  @Delete('roles/:roleId')
  @Roles(UserRole.ADMIN)
  @HttpCode(204)
  @ApiOperation({ summary: 'Delete role' })
  @ApiResponse({ status: 204, description: 'Role deleted' })
  async deleteRole(@Param('roleId') roleId: string) {
    await this.rolesService.deleteRole(roleId);
  }

  @Get('users/:userId/roles')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get user roles' })
  @ApiResponse({ status: 200, description: 'User roles', type: [Role] })
  async getUserRoles(@Param('userId') userId: string) {
    return this.usersService.adminListUserRoles(userId);
  }

  @Post('users/:userId/roles')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Assign role to user' })
  @ApiResponse({ status: 200, description: 'Role assigned' })
  async assignRoleToUser(@Param('userId') userId: string, @Body() body: any) {
    return this.usersService.adminAssignRoleToUser(userId, body.roleId);
  }

  @Delete('users/:userId/roles/:roleId')
  @Roles(UserRole.ADMIN)
  @HttpCode(204)
  @ApiOperation({ summary: 'Remove role from user' })
  @ApiResponse({ status: 204, description: 'Role removed' })
  async removeRoleFromUser(@Param('userId') userId: string, @Param('roleId') roleId: string) {
    await this.usersService.adminRemoveRoleFromUser(userId, roleId);
  }

  // === PERMISSIONS MANAGEMENT ===

  @Get('permissions')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'List all permissions' })
  @ApiResponse({ status: 200, description: 'List of permissions', type: [Object] })
  async listPermissions() {
    return this.permissionsService.listPermissions();
  }

  @Post('permissions')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new permission' })
  @ApiResponse({ status: 201, description: 'Permission created', type: Object })
  async createPermission(@Body() body: any) {
    return this.permissionsService.createPermission(body);
  }

  @Get('permissions/:permissionId')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get permission details' })
  @ApiResponse({ status: 200, description: 'Permission details', type: Object })
  async getPermission(@Param('permissionId') permissionId: string) {
    return this.permissionsService.getPermission(permissionId);
  }

  @Put('permissions/:permissionId')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update permission' })
  @ApiResponse({ status: 200, description: 'Permission updated', type: Object })
  async updatePermission(@Param('permissionId') permissionId: string, @Body() body: any) {
    return this.permissionsService.updatePermission(permissionId, body);
  }

  @Delete('permissions/:permissionId')
  @Roles(UserRole.ADMIN)
  @HttpCode(204)
  @ApiOperation({ summary: 'Delete permission' })
  @ApiResponse({ status: 204, description: 'Permission deleted' })
  async deletePermission(@Param('permissionId') permissionId: string) {
    await this.permissionsService.deletePermission(permissionId);
  }

  @Post('roles/:roleId/permissions')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Assign permission to role' })
  @ApiResponse({ status: 200, description: 'Permission assigned' })
  async assignPermissionToRole(@Param('roleId') roleId: string, @Body() body: any) {
    return this.rolesService.assignPermissionToRole(roleId, body.permissionId);
  }

  @Delete('roles/:roleId/permissions/:permissionId')
  @Roles(UserRole.ADMIN)
  @HttpCode(204)
  @ApiOperation({ summary: 'Remove permission from role' })
  @ApiResponse({ status: 204, description: 'Permission removed' })
  async removePermissionFromRole(@Param('roleId') roleId: string, @Param('permissionId') permissionId: string) {
    await this.rolesService.removePermissionFromRole(roleId, permissionId);
  }

  // === EMAIL MANAGEMENT ===

  @Post('email/templates')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new email template' })
  @ApiResponse({ status: 201, description: 'Template created successfully', type: EmailTemplateResponseDto })
  async createEmailTemplate(@Body() createDto: CreateEmailTemplateDto): Promise<EmailTemplateResponseDto> {
    return this.emailManagementService.createTemplate(createDto);
  }

  @Get('email/templates')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all email templates with filtering and pagination' })
  @ApiResponse({ status: 200, description: 'Templates retrieved successfully' })
  async getEmailTemplates(@Query() query: EmailTemplateQueryDto) {
    return this.emailManagementService.getTemplates(query);
  }

  @Get('email/templates/:id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get a specific email template by ID' })
  @ApiResponse({ status: 200, description: 'Template retrieved successfully', type: EmailTemplateResponseDto })
  async getEmailTemplate(@Param('id') id: string): Promise<EmailTemplateResponseDto> {
    return this.emailManagementService.getTemplate(id);
  }

  @Put('email/templates/:id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update an email template' })
  @ApiResponse({ status: 200, description: 'Template updated successfully', type: EmailTemplateResponseDto })
  async updateEmailTemplate(
    @Param('id') id: string,
    @Body() updateDto: UpdateEmailTemplateDto,
  ): Promise<EmailTemplateResponseDto> {
    return this.emailManagementService.updateTemplate(id, updateDto);
  }

  @Delete('email/templates/:id')
  @Roles(UserRole.ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete an email template' })
  @ApiResponse({ status: 204, description: 'Template deleted successfully' })
  async deleteEmailTemplate(@Param('id') id: string): Promise<void> {
    return this.emailManagementService.deleteTemplate(id);
  }

  @Post('email/campaigns')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new email campaign' })
  @ApiResponse({ status: 201, description: 'Campaign created successfully', type: EmailCampaignResponseDto })
  async createEmailCampaign(@Body() createDto: CreateEmailCampaignDto): Promise<EmailCampaignResponseDto> {
    return this.emailManagementService.createCampaign(createDto);
  }

  @Get('email/campaigns')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all email campaigns with filtering and pagination' })
  @ApiResponse({ status: 200, description: 'Campaigns retrieved successfully' })
  async getEmailCampaigns(@Query() query: EmailCampaignQueryDto) {
    return this.emailManagementService.getCampaigns(query);
  }

  @Get('email/campaigns/:id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get a specific email campaign by ID' })
  @ApiResponse({ status: 200, description: 'Campaign retrieved successfully', type: EmailCampaignResponseDto })
  async getEmailCampaign(@Param('id') id: string): Promise<EmailCampaignResponseDto> {
    return this.emailManagementService.getCampaign(id);
  }

  @Put('email/campaigns/:id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update an email campaign' })
  @ApiResponse({ status: 200, description: 'Campaign updated successfully', type: EmailCampaignResponseDto })
  async updateEmailCampaign(
    @Param('id') id: string,
    @Body() updateDto: UpdateEmailCampaignDto,
  ): Promise<EmailCampaignResponseDto> {
    return this.emailManagementService.updateCampaign(id, updateDto);
  }

  @Post('email/campaigns/:id/execute')
  @Roles(UserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Execute an email campaign' })
  @ApiResponse({ status: 200, description: 'Campaign execution started successfully' })
  async executeEmailCampaign(@Param('id') id: string): Promise<{ message: string }> {
    await this.emailManagementService.executeCampaign(id);
    return { message: 'Campaign execution started successfully' };
  }

  @Post('email/send')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Send email directly (with or without template)' })
  @ApiResponse({ status: 200, description: 'Email sent successfully' })
  async sendEmail(@Body() sendDto: SendEmailDto): Promise<{ success: boolean; logId: string }> {
    return this.emailManagementService.sendEmail(sendDto);
  }

  @Get('email/logs')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get email logs with filtering and pagination' })
  @ApiResponse({ status: 200, description: 'Email logs retrieved successfully' })
  async getEmailLogs(@Query() query: EmailLogQueryDto) {
    return this.emailManagementService.getEmailLogs(query);
  }

  @Get('email/analytics/summary')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get email analytics summary' })
  @ApiResponse({ status: 200, description: 'Analytics summary retrieved successfully' })
  async getEmailAnalyticsSummary() {
    return this.emailManagementService.getAnalyticsSummary();
  }

  @Get('email/analytics/templates/:id/stats')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get statistics for a specific template' })
  @ApiResponse({ status: 200, description: 'Template statistics retrieved successfully' })
  async getEmailTemplateStats(@Param('id') id: string) {
    return this.emailManagementService.getTemplateStats(id);
  }

  @Get('email/analytics/campaigns/:id/stats')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get statistics for a specific campaign' })
  @ApiResponse({ status: 200, description: 'Campaign statistics retrieved successfully' })
  async getEmailCampaignStats(@Param('id') id: string) {
    return this.emailManagementService.getCampaignStats(id);
  }
}