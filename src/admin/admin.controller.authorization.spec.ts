/**
 * AdminController Authorization Tests
 * Focused on testing all authorization branches to improve branch coverage
 */

import { ForbiddenException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { AdminController } from './admin.controller.js';
import { PermissionsService } from './permissions.service.js';
import { RolesService } from './roles.service.js';
import { UsersService } from '../users/users.service.js';

describe('AdminController Authorization Tests', () => {
  let controller: AdminController;
  let usersService: jest.Mocked<UsersService>;
  let rolesService: jest.Mocked<RolesService>;
  let permissionsService: jest.Mocked<PermissionsService>;

  beforeEach(async () => {
    const mockUsersService = {
      getUserSessions: jest.fn(),
      removeUserSession: jest.fn(),
      adminListUsers: jest.fn(),
      adminCreateUser: jest.fn(),
      adminGetUser: jest.fn(),
      adminUpdateUser: jest.fn(),
      adminDeleteUser: jest.fn(),
      adminListUserRoles: jest.fn(),
      adminAssignRoleToUser: jest.fn(),
      adminRemoveRoleFromUser: jest.fn(),
    };

    const mockRolesService = {
      listRoles: jest.fn(),
      createRole: jest.fn(),
      getRole: jest.fn(),
      updateRole: jest.fn(),
      deleteRole: jest.fn(),
      assignPermissionToRole: jest.fn(),
      removePermissionFromRole: jest.fn(),
    };

    const mockPermissionsService = {
      listPermissions: jest.fn(),
      createPermission: jest.fn(),
      getPermission: jest.fn(),
      updatePermission: jest.fn(),
      deletePermission: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminController],
      providers: [
        { provide: UsersService, useValue: mockUsersService },
        { provide: RolesService, useValue: mockRolesService },
        { provide: PermissionsService, useValue: mockPermissionsService },
      ],
    }).compile();

    controller = module.get<AdminController>(AdminController);
    usersService = module.get(UsersService);
    rolesService = module.get(RolesService);
    permissionsService = module.get(PermissionsService);
  });

  describe('Authorization Branches - Non-Admin Users', () => {
    const nonAdminRequest = { user: { roles: ['user'] } };
    const noRolesRequest = { user: {} };
    const noUserRequest = {};

    it('should throw ForbiddenException for getUserSessions without admin role', async () => {
      await expect(controller.getUserSessions('user-id', nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.getUserSessions('user-id', noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.getUserSessions('user-id', noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for revokeUserSession without admin role', async () => {
      await expect(controller.revokeUserSession('user-id', 'session-id', nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.revokeUserSession('user-id', 'session-id', noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.revokeUserSession('user-id', 'session-id', noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for listUsers without admin role', async () => {
      await expect(controller.listUsers(1, 20, '', '', '', nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.listUsers(1, 20, '', '', '', noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.listUsers(1, 20, '', '', '', noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for createUser without admin role', async () => {
      const userData = { email: '<EMAIL>' };
      await expect(controller.createUser(userData, nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.createUser(userData, noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.createUser(userData, noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for getUser without admin role', async () => {
      await expect(controller.getUser('user-id', nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.getUser('user-id', noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.getUser('user-id', noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for updateUser without admin role', async () => {
      const updateData = { name: 'Updated' };
      await expect(controller.updateUser('user-id', updateData, nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.updateUser('user-id', updateData, noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.updateUser('user-id', updateData, noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for deleteUser without admin role', async () => {
      await expect(controller.deleteUser('user-id', nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.deleteUser('user-id', noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.deleteUser('user-id', noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for listRoles without admin role', async () => {
      await expect(controller.listRoles(nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.listRoles(noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.listRoles(noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for createRole without admin role', async () => {
      const roleData = { name: 'test-role' };
      await expect(controller.createRole(roleData, nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.createRole(roleData, noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.createRole(roleData, noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for getRole without admin role', async () => {
      await expect(controller.getRole('role-id', nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.getRole('role-id', noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.getRole('role-id', noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for updateRole without admin role', async () => {
      const updateData = { name: 'updated-role' };
      await expect(controller.updateRole('role-id', updateData, nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.updateRole('role-id', updateData, noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.updateRole('role-id', updateData, noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for deleteRole without admin role', async () => {
      await expect(controller.deleteRole('role-id', nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.deleteRole('role-id', noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.deleteRole('role-id', noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for getUserRoles without admin role', async () => {
      await expect(controller.getUserRoles('user-id', nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.getUserRoles('user-id', noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.getUserRoles('user-id', noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for assignRoleToUser without admin role', async () => {
      const roleData = { roleId: 'role-id' };
      await expect(controller.assignRoleToUser('user-id', roleData, nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.assignRoleToUser('user-id', roleData, noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.assignRoleToUser('user-id', roleData, noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for removeRoleFromUser without admin role', async () => {
      await expect(controller.removeRoleFromUser('user-id', 'role-id', nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.removeRoleFromUser('user-id', 'role-id', noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.removeRoleFromUser('user-id', 'role-id', noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for listPermissions without admin role', async () => {
      await expect(controller.listPermissions(nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.listPermissions(noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.listPermissions(noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for createPermission without admin role', async () => {
      const permissionData = { name: 'test-permission' };
      await expect(controller.createPermission(permissionData, nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.createPermission(permissionData, noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.createPermission(permissionData, noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for getPermission without admin role', async () => {
      await expect(controller.getPermission('permission-id', nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.getPermission('permission-id', noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.getPermission('permission-id', noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for updatePermission without admin role', async () => {
      const updateData = { name: 'updated-permission' };
      await expect(controller.updatePermission('permission-id', updateData, nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.updatePermission('permission-id', updateData, noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.updatePermission('permission-id', updateData, noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for deletePermission without admin role', async () => {
      await expect(controller.deletePermission('permission-id', nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.deletePermission('permission-id', noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.deletePermission('permission-id', noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for assignPermissionToRole without admin role', async () => {
      const permissionData = { permissionId: 'permission-id' };
      await expect(controller.assignPermissionToRole('role-id', permissionData, nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.assignPermissionToRole('role-id', permissionData, noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.assignPermissionToRole('role-id', permissionData, noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException for removePermissionFromRole without admin role', async () => {
      await expect(controller.removePermissionFromRole('role-id', 'permission-id', nonAdminRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.removePermissionFromRole('role-id', 'permission-id', noRolesRequest))
        .rejects.toThrow(ForbiddenException);
      await expect(controller.removePermissionFromRole('role-id', 'permission-id', noUserRequest))
        .rejects.toThrow(ForbiddenException);
    });
  });
});
